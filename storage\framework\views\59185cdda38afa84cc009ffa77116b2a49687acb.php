<div class="d-flex align-items-center">
          <!-- User Dropdown -->
          <div class="dropdown d-inline-block ms-2">
            <button type="button" class="btn btn-sm btn-alt-secondary d-flex align-items-center" id="page-header-user-dropdown" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <img class="rounded-circle" src="<?php echo e(asset('media/avatars/avatar10.jpg')); ?>" alt="Header Avatar" style="width: 21px;">
              <span class="d-none d-sm-inline-block ms-2"><?php echo e(userFullName()); ?></span>
              <i class="fa fa-fw fa-angle-down d-none d-sm-inline-block ms-1 mt-1"></i>
            </button>
            <div class="dropdown-menu dropdown-menu-md dropdown-menu-end p-0 border-0" aria-labelledby="page-header-user-dropdown">
              <div class="p-3 text-center bg-body-light border-bottom rounded-top">
                <img class="img-avatar img-avatar48 img-avatar-thumb" src="<?php echo e(asset('media/avatars/avatar10.jpg')); ?>" alt="">
                <p class="mt-2 mb-0 fw-medium"><?php echo e(userFullName()); ?></p>
                <p class="mb-0 text-muted fs-sm fw-medium"><?php echo e(getRolesName()); ?></p>
              </div>
              <div class="p-2">
                
                <a class="dropdown-item d-flex align-items-center justify-content-between" href="<?php echo e(route('change-password')); ?>">
                  <span class="fs-sm fw-medium">Changer Mot de passe</span>
                  
                </a>
                
              </div>
              <div role="separator" class="dropdown-divider m-0"></div>
              <div class="p-2">
                
                <a class="dropdown-item d-flex align-items-center justify-content-between" href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                  <span class="fs-sm fw-medium">Déconnecter</span>
                </a>
                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                  <?php echo csrf_field(); ?>
                </form>
              </div>
            </div>
          </div>
          <!-- END User Dropdown -->

          <!-- Notifications Dropdown -->
          
          <!-- END Notifications Dropdown -->

          <!-- Toggle Side Overlay -->
          <!-- Layout API, functionality initialized in Template._uiApiLayout() -->
          
          <!-- END Toggle Side Overlay -->
        </div><?php /**PATH C:\xampp\htdocs\ImsaaProject\resources\views/components/rightHeader.blade.php ENDPATH**/ ?>