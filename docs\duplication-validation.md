# Validation de Duplication des UE et EC

## Vue d'ensemble

Ce document décrit les améliorations apportées au système de validation pour la fonctionnalité de duplication des UE (Unités d'Enseignement) et EC (Éléments Constitutifs) dans le projet IMSAA.

## Fonctionnalités de Validation

### 1. Validation de Sélection

Avant de passer à l'étape d'aperçu, le système valide :

- **Sélection d'UE** : Au moins une UE doit être sélectionnée
- **Parcours de destination** : Au moins un parcours doit être sélectionné
- **Année universitaire** : Une année universitaire de destination doit être sélectionnée
- **Existence des entités** : Vérification que toutes les UE, parcours et années sélectionnés existent encore
- **Détection de conflits** : Identification des UE qui existent déjà dans les parcours de destination

### 2. Validation des Données d'Aperçu

Lors de l'étape d'aperçu, le système valide les données modifiables :

#### Validation des UE
- **Nom** : Obligatoire, maximum 255 caractères
- **Code** : Obligatoire, maximum 50 caractères, format alphanumériqueque avec tirets, underscores et points
- **Crédit** : Obligatoire, nombre positif
- **Unicité** : Vérification que les codes UE sont uniques dans le lot de duplication

#### Validation des EC
- **Nom** : Obligatoire, maximum 255 caractères
- **Code** : Obligatoire, maximum 50 caractères, format alphanumériqueque avec tirets, underscores et points
- **Enseignant** : Optionnel, mais si fourni, doit exister et avoir le rôle d'enseignant
- **Unicité** : Vérification que les codes EC sont uniques au sein de chaque UE

### 3. Interface Utilisateur

#### Indicateurs Visuels
- **États de validation** : pending, validating, valid, invalid
- **Couleurs** : Vert pour valide, rouge pour invalide, bleu pour en attente
- **Icônes** : Indicateurs visuels pour chaque état

#### Validation en Temps Réel
- **Champs obligatoires** : Validation immédiate lors de la saisie
- **Limites de caractères** : Vérification en temps réel
- **Format des codes** : Validation du format lors de la saisie

#### Bouton de Validation Manuelle
- Permet à l'utilisateur de valider manuellement les données
- Affiche les résultats de validation en temps réel
- Désactive le bouton de confirmation si des erreurs sont détectées

## Méthodes de Validation

### `validateDuplicationSelection()`
Valide la sélection initiale avant de passer à l'aperçu.

### `validateDuplicationData()`
Valide toutes les données avant l'exécution de la duplication.

### `validateUeData($ueData, $ueId)`
Valide les données d'une UE spécifique.

### `validateEcData($ecData, $ueId)`
Valide les données des EC d'une UE spécifique.

### `validatePreviewData()`
Méthode publique pour la validation manuelle depuis l'interface.

## Gestion des Erreurs

### Messages d'Erreur Détaillés
- Messages spécifiques pour chaque type d'erreur
- Identification claire de l'UE ou EC concerné
- Suggestions de correction

### Prévention des Conflits
- Détection des UE existantes
- Avertissements sans blocage (les UE existantes ne seront pas remplacées)
- Vérification de l'unicité des codes

## Améliorations Futures Possibles

1. **Validation asynchrone** : Validation en arrière-plan pendant la saisie
2. **Suggestions automatiques** : Propositions de codes uniques en cas de conflit
3. **Validation par lot** : Validation de plusieurs UE simultanément
4. **Historique de validation** : Sauvegarde des résultats de validation
5. **Règles de validation personnalisables** : Configuration des règles par institution

## Utilisation

1. **Sélection** : Choisir les UE et destinations
2. **Aperçu** : Modifier les données si nécessaire
3. **Validation** : Cliquer sur "Valider maintenant" ou laisser la validation automatique
4. **Confirmation** : Procéder à la duplication une fois les données validées

Le système garantit l'intégrité des données et une expérience utilisateur fluide avec des retours visuels clairs à chaque étape.
