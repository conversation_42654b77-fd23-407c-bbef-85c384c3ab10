{"__meta": {"id": "Xd3e10c1c591c3cefa46aae34ea35e4c7", "datetime": "2025-09-17 11:27:16", "utime": **********.102738, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 3, "messages": [{"message": "[11:27:16] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.095279, "collector": "log"}, {"message": "[11:27:16] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Niveau\r\nNum-Called: 4\r\nCall-Stack:\r\n#19 \\app\\Http\\Livewire\\Ues.php:653\r\n#20 \\app\\Http\\Livewire\\Ues.php:640\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#25 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#26 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#27 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#28 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#29 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.100363, "collector": "log"}, {"message": "[11:27:16] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Semestre\r\nNum-Called: 4\r\nCall-Stack:\r\n#19 \\app\\Http\\Livewire\\Ues.php:653\r\n#20 \\app\\Http\\Livewire\\Ues.php:640\r\n#21 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:36\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php:41\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:93\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php:35\r\n#25 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php:149\r\n#26 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php:36\r\n#27 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:89\r\n#28 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:13\r\n#29 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#32 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#33 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#34 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#35 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php:78\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#39 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php:49\r\n#40 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#41 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:121\r\n#42 \\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php:64\r\n#43 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php:37\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php:67\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:180\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:116\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:797\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.100641, "collector": "log"}]}, "time": {"start": **********.185916, "end": **********.102775, "duration": 0.****************, "duration_str": "917ms", "measures": [{"label": "Booting", "start": **********.185916, "relative_start": 0, "end": **********.534649, "relative_end": **********.534649, "duration": 0.*****************, "duration_str": "349ms", "params": [], "collector": null}, {"label": "Application", "start": **********.535301, "relative_start": 0.*****************, "end": **********.102778, "relative_end": 2.86102294921875e-06, "duration": 0.****************, "duration_str": "567ms", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 45, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 47, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 53, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 48, "nb_failed_statements": 0, "accumulated_duration": 0.05245999999999999, "accumulated_duration_str": "52.46ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00477, "duration_str": "4.77ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 9.093}, {"sql": "select * from `ues` where `ues`.`id` = 301 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["301"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 9.093, "width_percent": 1.449}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (301) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 10.541, "width_percent": 1.563}, {"sql": "select * from `users` where `users`.`id` in (149, 152) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0010400000000000001, "duration_str": "1.04ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 12.104, "width_percent": 1.982}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00066, "duration_str": "660μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 14.087, "width_percent": 1.258}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 15.345, "width_percent": 1.506}, {"sql": "select * from `parcours` where `parcours`.`id` = '3' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Models\\Ue.php:226", "connection": "imsaaapp", "start_percent": 16.851, "width_percent": 1.449}, {"sql": "select * from `ues` where `code` = 'IMTHRL11ENJUFI' and `parcour_id` = '3' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTHRL11ENJUFI", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 238}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Models\\Ue.php:238", "connection": "imsaaapp", "start_percent": 18.3, "width_percent": 2.116}, {"sql": "select * from `ues` where `ues`.`id` = 302 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["302"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00069, "duration_str": "690μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 20.416, "width_percent": 1.315}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (302) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 21.731, "width_percent": 1.544}, {"sql": "select * from `users` where `users`.`id` in (148, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 23.275, "width_percent": 1.906}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 25.181, "width_percent": 1.411}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 26.592, "width_percent": 1.83}, {"sql": "select * from `parcours` where `parcours`.`id` = '3' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Models\\Ue.php:226", "connection": "imsaaapp", "start_percent": 28.422, "width_percent": 1.525}, {"sql": "select * from `ues` where `code` = 'IMTHRL11LANCOM' and `parcour_id` = '3' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTHRL11LANCOM", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 238}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00141, "duration_str": "1.41ms", "stmt_id": "\\app\\Models\\Ue.php:238", "connection": "imsaaapp", "start_percent": 29.947, "width_percent": 2.688}, {"sql": "select * from `ues` where `ues`.`id` = 303 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["303"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 32.634, "width_percent": 1.506}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (303) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0009599999999999999, "duration_str": "960μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 34.14, "width_percent": 1.83}, {"sql": "select * from `users` where `users`.`id` in (148, 149) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00124, "duration_str": "1.24ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 35.97, "width_percent": 2.364}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 38.334, "width_percent": 1.372}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 39.706, "width_percent": 1.43}, {"sql": "select * from `parcours` where `parcours`.`id` = '3' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00189, "duration_str": "1.89ms", "stmt_id": "\\app\\Models\\Ue.php:226", "connection": "imsaaapp", "start_percent": 41.136, "width_percent": 3.603}, {"sql": "select * from `ues` where `code` = 'IMTHRL11' and `parcour_id` = '3' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTHRL11", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 238}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Models\\Ue.php:238", "connection": "imsaaapp", "start_percent": 44.739, "width_percent": 2.021}, {"sql": "select * from `ues` where `ues`.`id` = 304 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["304"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 46.759, "width_percent": 1.334}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (304) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 48.094, "width_percent": 1.754}, {"sql": "select * from `users` where `users`.`id` in (145, 147) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 26, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 27, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00101, "duration_str": "1.01ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 49.848, "width_percent": 1.925}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00094, "duration_str": "940μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 51.773, "width_percent": 1.792}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 653}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:653", "connection": "imsaaapp", "start_percent": 53.565, "width_percent": 1.449}, {"sql": "select * from `parcours` where `parcours`.`id` = '3' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 226}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0011799999999999998, "duration_str": "1.18ms", "stmt_id": "\\app\\Models\\Ue.php:226", "connection": "imsaaapp", "start_percent": 55.013, "width_percent": 2.249}, {"sql": "select * from `ues` where `code` = 'IMTHRL11' and `parcour_id` = '3' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTHRL11", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 238}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 655}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 640}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Models\\Ue.php:238", "connection": "imsaaapp", "start_percent": 57.263, "width_percent": 1.677}, {"sql": "select * from `ues` where `parcour_id` in ('1') and `semestre_id` in ('1') and `annee_universitaire_id` = '5' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 58.94, "width_percent": 2.936}, {"sql": "select * from `parcours` where `parcours`.`id` in (1) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 61.876, "width_percent": 1.353}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0008399999999999999, "duration_str": "840μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 63.229, "width_percent": 1.601}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 64.83, "width_percent": 1.239}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 66.069, "width_percent": 1.353}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (301, 302, 303, 304) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 67.423, "width_percent": 1.754}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 69.177, "width_percent": 1.334}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 70.511, "width_percent": 1.735}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007700000000000001, "duration_str": "770μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 72.246, "width_percent": 1.468}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00139, "duration_str": "1.39ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 73.713, "width_percent": 2.65}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 76.363, "width_percent": 1.677}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 78.04, "width_percent": 1.372}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 79.413, "width_percent": 3.469}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0014399999999999999, "duration_str": "1.44ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 82.882, "width_percent": 2.745}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 85.627, "width_percent": 1.353}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 86.981, "width_percent": 1.754}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 88.734, "width_percent": 1.296}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 90.03, "width_percent": 1.277}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 128}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00456, "duration_str": "4.56ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:128", "connection": "imsaaapp", "start_percent": 91.308, "width_percent": 8.692}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 9, "App\\Models\\Parcour": 32, "App\\Models\\Semestre": 16, "App\\Models\\Niveau": 11, "App\\Models\\Matiere": 37, "App\\Models\\Ue": 18, "App\\Models\\User": 72}, "count": 195}, "livewire": {"data": {"ues #sJT7BchAE2L12UyAchxq": "array:5 [\n  \"data\" => array:35 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"preview\"\n    \"selectedUesForDuplication\" => array:4 [\n      0 => 301\n      1 => 302\n      2 => 303\n      3 => 304\n    ]\n    \"targetParcours\" => array:1 [\n      0 => \"3\"\n    ]\n    \"targetAnneeId\" => \"7\"\n    \"duplicationPreview\" => array:4 [\n      301 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 301\n          \"nom\" => \"ENVIRONNEMENT JURIDIQUE ET FISCAL\"\n          \"code\" => \"IMTHRL11ENJUFI\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 489\n            \"nom\" => \"Initiation juridique\"\n            \"code\" => \"INIJUR\"\n            \"syllabus\" => null\n            \"user_id\" => 152\n            \"enseignant_nom\" => \" Marie ANNE\"\n          ]\n          1 => array:6 [\n            \"id\" => 490\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISCALT\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      302 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 302\n          \"nom\" => \"LANGUE ET COMMUNICATION\"\n          \"code\" => \"IMTHRL11LANCOM\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 491\n            \"nom\" => \"Communication d'entreprise\"\n            \"code\" => \"COMENTR\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 492\n            \"nom\" => \"Italien\"\n            \"code\" => \"ITALIE\"\n            \"syllabus\" => null\n            \"user_id\" => 180\n            \"enseignant_nom\" => \" ANDREAS\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      303 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 303\n          \"nom\" => \"GESTION ET COMPTABILITÉ\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 493\n            \"nom\" => \"Organisation\"\n            \"code\" => \"ORGAN\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 494\n            \"nom\" => \"Comptabilité \"\n            \"code\" => \"COMPTAGENL\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      304 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 304\n          \"nom\" => \"INSERTION PROFESSIONNELLE\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 495\n            \"nom\" => \"Visite d'entreprise\"\n            \"code\" => \"VISITEENTRE\"\n            \"syllabus\" => null\n            \"user_id\" => 147\n            \"enseignant_nom\" => \"RAMINISOA Jerry Léoness\"\n          ]\n          1 => array:6 [\n            \"id\" => 496\n            \"nom\" => \"Informatique\"\n            \"code\" => \"INFORMA\"\n            \"syllabus\" => null\n            \"user_id\" => 145\n            \"enseignant_nom\" => \"RANAIVOSOA Edwino\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n    ]\n    \"editablePreviewData\" => array:4 [\n      301 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 301\n          \"nom\" => \"ENVIRONNEMENT JURIDIQUE ET FISCAL\"\n          \"code\" => \"IMTHRL11ENJUFI\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 489\n            \"nom\" => \"Initiation juridique\"\n            \"code\" => \"INIJUR\"\n            \"syllabus\" => null\n            \"user_id\" => 152\n            \"enseignant_nom\" => \" Marie ANNE\"\n          ]\n          1 => array:6 [\n            \"id\" => 490\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISCALT\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n      302 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 302\n          \"nom\" => \"LANGUE ET COMMUNICATION\"\n          \"code\" => \"IMTHRL11LANCOM\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 491\n            \"nom\" => \"Communication d'entreprise\"\n            \"code\" => \"COMENTR\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 492\n            \"nom\" => \"Italien\"\n            \"code\" => \"ITALIE\"\n            \"syllabus\" => null\n            \"user_id\" => 180\n            \"enseignant_nom\" => \" ANDREAS\"\n          ]\n        ]\n      ]\n      303 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 303\n          \"nom\" => \"GESTION ET COMPTABILITÉ\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 493\n            \"nom\" => \"Organisation\"\n            \"code\" => \"ORGAN\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 494\n            \"nom\" => \"Comptabilité \"\n            \"code\" => \"COMPTAGENL\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n      304 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 304\n          \"nom\" => \"INSERTION PROFESSIONNELLE\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 495\n            \"nom\" => \"Visite d'entreprise\"\n            \"code\" => \"VISITEENTRE\"\n            \"syllabus\" => null\n            \"user_id\" => 147\n            \"enseignant_nom\" => \"RAMINISOA Jerry Léoness\"\n          ]\n          1 => array:6 [\n            \"id\" => 496\n            \"nom\" => \"Informatique\"\n            \"code\" => \"INFORMA\"\n            \"syllabus\" => null\n            \"user_id\" => 145\n            \"enseignant_nom\" => \"RANAIVOSOA Edwino\"\n          ]\n        ]\n      ]\n    ]\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"5\"\n    \"duplicationFilterParcours\" => array:1 [\n      0 => \"1\"\n    ]\n    \"duplicationFilterSemestres\" => array:1 [\n      0 => \"1\"\n    ]\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => true\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"sJT7BchAE2L12UyAchxq\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1312804162 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1312804162\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1101381126 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1101381126\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-999909555 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">sJT7BchAE2L12UyAchxq</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">948b2fd5</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:35</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">selection</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-num>301</span>\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-num>302</span>\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-num>303</span>\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-num>304</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>3</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => []\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => []\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>duplicationFilterSemestres</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">b90e42dd0f1fbb68bb0ac3541e3117f8b0f294604c093b3570033b942fc31de0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1v4h</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"16 characters\">proceedToPreview</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-999909555\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1567351030 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1231</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9Rak5UU2s0YTQ0d1lmSCsyaDg5U0E9PSIsInZhbHVlIjoiUC9KS1pndUpBSUJSaWlhR3k0OVZsRjYyenVlZXJlMzd2UjVDUlM0cGcrSk01aVJkK0x1ZDd3RmFsQ09GSmdwWXN1ZXIycmdSRXJ1a29sYzdGQS9pSWJZTHl4OWhqcWxHRUsvNUlQciszQTlCazBRYTl4aisyNTNKMVFoNGlKSUMiLCJtYWMiOiJmMjQyYjA1ZWI1NDRjODA0OWZiZWM0MmM5MjhiNjcyMDMyYTYxYWI4MWFhYjcyOWM2ZjJkNmIwOTM4ZWNlNWU3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ik4rYXl2MjQwYk9yOE1LRU8xNVlsWUE9PSIsInZhbHVlIjoiVFNoQVZhWEdvREIxdXBnaU9IRWdybzloaDloZ1hOck5BY3NZSTNaMWV2T0tYbG1lRWU4WmZvczZvdWFDdmpPQkR2QXdrMlFHRTF2VWtNNzZaRHNHWkpiMXl1blV5Q2FsVEpxOXpBRVZCQTRaNkloLzg1NGs4enlYUU5MaFZ0MEkiLCJtYWMiOiI4YTQ0YWEyZTg2MDljMmJkZmIzZmMwOWE3N2FhZWM2NjZhNGEwOWNhMDc1NTQ2MjhiMWJmZDFjOWI1YmRjMjgzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1567351030\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-613605388 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60519</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1231</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1231</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Im9Rak5UU2s0YTQ0d1lmSCsyaDg5U0E9PSIsInZhbHVlIjoiUC9KS1pndUpBSUJSaWlhR3k0OVZsRjYyenVlZXJlMzd2UjVDUlM0cGcrSk01aVJkK0x1ZDd3RmFsQ09GSmdwWXN1ZXIycmdSRXJ1a29sYzdGQS9pSWJZTHl4OWhqcWxHRUsvNUlQciszQTlCazBRYTl4aisyNTNKMVFoNGlKSUMiLCJtYWMiOiJmMjQyYjA1ZWI1NDRjODA0OWZiZWM0MmM5MjhiNjcyMDMyYTYxYWI4MWFhYjcyOWM2ZjJkNmIwOTM4ZWNlNWU3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6Ik4rYXl2MjQwYk9yOE1LRU8xNVlsWUE9PSIsInZhbHVlIjoiVFNoQVZhWEdvREIxdXBnaU9IRWdybzloaDloZ1hOck5BY3NZSTNaMWV2T0tYbG1lRWU4WmZvczZvdWFDdmpPQkR2QXdrMlFHRTF2VWtNNzZaRHNHWkpiMXl1blV5Q2FsVEpxOXpBRVZCQTRaNkloLzg1NGs4enlYUU5MaFZ0MEkiLCJtYWMiOiI4YTQ0YWEyZTg2MDljMmJkZmIzZmMwOWE3N2FhZWM2NjZhNGEwOWNhMDc1NTQ2MjhiMWJmZDFjOWI1YmRjMjgzIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.1859</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613605388\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-463500347 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-463500347\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-657433008 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 08:27:16 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Impoa3NPaGhMM0NPUkxQVTdBQkRhbEE9PSIsInZhbHVlIjoiMjFLcDlNMjVxbG9JUGltQmJXYTVHQVlqMTZwTVo3R2krZUNlOTZxdDN2YTFEcmRUN0p2Lzk2UEE0ZFZ0ZHVMUndUS3BFOHQ4NEx4UEwzbWtNOU16QTJvckFiQUhjM2tlTDlPUmFzL3ZBYzZrUGR3VnRLbHVmZ2pmTW90YnUwb3oiLCJtYWMiOiJiMmQxMDdmZTA4ZGJlYmZiN2FlNmJlNTRkMzM0ODUzODMwYmEyYWM1MDUwNjU0NTQzMTQwZjFhN2ViYTBiNTU0IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:27:16 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IjJ1NnY3M3FUQXE4T25McE85RFBjMnc9PSIsInZhbHVlIjoiZHZhK1ovZitsdFdRSi9CTTZaN1hHaHZJN0g5dDdtbDVEQ1ozVk9oUmdvRGo2Y0NGNjZPUGRuSzJrdDhpdklHNWpoT281blg2dWFESDBTRWo0amcwZkNsT0NzNHVOd2J4dktrV2tWbU9xMk9IVS9OKzNGTnVXV0JRNlZXZXRxRG8iLCJtYWMiOiI4OTIzNTczN2U0ZDI3OTgyZTk5ZDk5ZTY3ZWVjM2RiZDAzMmJiMGE0ZjE0ZTZjZTQ5ZGRjNmRkODMyYTUwY2JhIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:27:16 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Impoa3NPaGhMM0NPUkxQVTdBQkRhbEE9PSIsInZhbHVlIjoiMjFLcDlNMjVxbG9JUGltQmJXYTVHQVlqMTZwTVo3R2krZUNlOTZxdDN2YTFEcmRUN0p2Lzk2UEE0ZFZ0ZHVMUndUS3BFOHQ4NEx4UEwzbWtNOU16QTJvckFiQUhjM2tlTDlPUmFzL3ZBYzZrUGR3VnRLbHVmZ2pmTW90YnUwb3oiLCJtYWMiOiJiMmQxMDdmZTA4ZGJlYmZiN2FlNmJlNTRkMzM0ODUzODMwYmEyYWM1MDUwNjU0NTQzMTQwZjFhN2ViYTBiNTU0IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:27:16 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IjJ1NnY3M3FUQXE4T25McE85RFBjMnc9PSIsInZhbHVlIjoiZHZhK1ovZitsdFdRSi9CTTZaN1hHaHZJN0g5dDdtbDVEQ1ozVk9oUmdvRGo2Y0NGNjZPUGRuSzJrdDhpdklHNWpoT281blg2dWFESDBTRWo0amcwZkNsT0NzNHVOd2J4dktrV2tWbU9xMk9IVS9OKzNGTnVXV0JRNlZXZXRxRG8iLCJtYWMiOiI4OTIzNTczN2U0ZDI3OTgyZTk5ZDk5ZTY3ZWVjM2RiZDAzMmJiMGE0ZjE0ZTZjZTQ5ZGRjNmRkODMyYTUwY2JhIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:27:16 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-657433008\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-372695821 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-372695821\", {\"maxDepth\":0})</script>\n"}}