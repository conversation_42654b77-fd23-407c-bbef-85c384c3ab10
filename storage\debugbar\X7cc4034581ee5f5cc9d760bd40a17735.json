{"__meta": {"id": "X7cc4034581ee5f5cc9d760bd40a17735", "datetime": "2025-09-17 10:42:59", "utime": **********.191995, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 6, "messages": [{"message": "[10:42:59] LOG.info: Detected N+1 Query", "message_html": null, "is_string": false, "label": "info", "time": **********.18755, "collector": "log"}, {"message": "[10:42:59] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Parcour\r\nNum-Called: 2\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.188529, "collector": "log"}, {"message": "[10:42:59] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Niveau\r\nNum-Called: 2\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.188849, "collector": "log"}, {"message": "[10:42:59] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Semestre\r\nNum-Called: 2\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.189117, "collector": "log"}, {"message": "[10:42:59] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\AnneeUniversitaire\r\nNum-Called: 2\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.189324, "collector": "log"}, {"message": "[10:42:59] LOG.info: Model: App\\Models\\Ue\r\nRelation: App\\Models\\Matiere\r\nNum-Called: 2\r\nCall-Stack:\r\n#17 \\app\\Http\\Livewire\\Ues.php:567\r\n#18 \\storage\\framework\\views\\0b5e646256704a78cdceca5c9decbf8c72b1cd7e.php:100\r\n#20 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#21 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#22 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#23 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#24 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#25 \\storage\\framework\\views\\0fc782b3a78481746a35719046fed38e9c2da4d1.php:667\r\n#27 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#28 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#29 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#30 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#31 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#32 \\storage\\framework\\views\\ec55c87cdf6f1b4f08153ee7f62e605228e2d462.php:18\r\n#34 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:84\r\n#35 \\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php:59\r\n#36 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:195\r\n#37 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:178\r\n#38 \\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php:147\r\n#39 \\vendor\\livewire\\livewire\\src\\Component.php:235\r\n#40 \\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\RenderView.php:14\r\n#41 \\vendor\\livewire\\livewire\\src\\LifecycleManager.php:154\r\n#42 \\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php:15\r\n#43 \\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php:18\r\n#44 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php:46\r\n#45 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:259\r\n#46 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php:205\r\n#47 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php:798\r\n#48 \\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php:141\r\n#49 \\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php:50\r\n", "message_html": null, "is_string": false, "label": "info", "time": **********.189534, "collector": "log"}]}, "time": {"start": **********.73981, "end": **********.192037, "duration": 1.****************, "duration_str": "1.45s", "measures": [{"label": "Booting", "start": **********.73981, "relative_start": 0, "end": **********.336491, "relative_end": **********.336491, "duration": 0.****************, "duration_str": "597ms", "params": [], "collector": null}, {"label": "Application", "start": **********.338018, "relative_start": 0.***************, "end": **********.192041, "relative_end": 3.814697265625e-06, "duration": 0.****************, "duration_str": "854ms", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "29MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 44, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 46, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 52, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 26, "nb_failed_statements": 0, "accumulated_duration": 0.17173000000000002, "accumulated_duration_str": "172ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.006889999999999999, "duration_str": "6.89ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 4.012}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00525, "duration_str": "5.25ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 4.012, "width_percent": 3.057}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 18, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00328, "duration_str": "3.28ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 7.069, "width_percent": 1.91}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 8.979, "width_percent": 0.757}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00083, "duration_str": "830μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 9.736, "width_percent": 0.483}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (3, 4, 5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00088, "duration_str": "880μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 10.22, "width_percent": 0.512}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 526}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 118}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.01342, "duration_str": "13.42ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 10.732, "width_percent": 7.815}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00103, "duration_str": "1.03ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 18.547, "width_percent": 0.6}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0016, "duration_str": "1.6ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 19.146, "width_percent": 0.932}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00163, "duration_str": "1.63ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 20.078, "width_percent": 0.949}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 21.027, "width_percent": 0.413}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00799, "duration_str": "7.99ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 21.441, "width_percent": 4.653}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.01702, "duration_str": "17.02ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 26.093, "width_percent": 9.911}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00357, "duration_str": "3.57ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 36.004, "width_percent": 2.079}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 122}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00394, "duration_str": "3.94ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:122", "connection": "imsaaapp", "start_percent": 38.083, "width_percent": 2.294}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00382, "duration_str": "3.82ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 40.377, "width_percent": 2.224}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0059299999999999995, "duration_str": "5.93ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 42.602, "width_percent": 3.453}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00424, "duration_str": "4.24ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 46.055, "width_percent": 2.469}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00746, "duration_str": "7.46ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 48.524, "width_percent": 4.344}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.009880000000000002, "duration_str": "9.88ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 52.868, "width_percent": 5.753}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 15, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.00512, "duration_str": "5.12ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 58.621, "width_percent": 2.981}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 18, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.0123, "duration_str": "12.3ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 61.603, "width_percent": 7.162}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.01427, "duration_str": "14.27ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 68.765, "width_percent": 8.31}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.01918, "duration_str": "19.18ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 77.074, "width_percent": 11.169}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (3, 4, 5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.007719999999999999, "duration_str": "7.72ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 88.243, "width_percent": 4.495}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 567}, {"index": 20, "namespace": "view", "name": "0b5e646256704a78cdceca5c9decbf8c72b1cd7e", "line": 100}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 195}], "duration": 0.01247, "duration_str": "12.47ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:567", "connection": "imsaaapp", "start_percent": 92.739, "width_percent": 7.261}]}, "models": {"data": {"App\\Models\\Matiere": 1509, "App\\Models\\AnneeUniversitaire": 14, "App\\Models\\Semestre": 25, "App\\Models\\Niveau": 12, "App\\Models\\Parcour": 51, "App\\Models\\Ue": 736, "App\\Models\\User": 64}, "count": 2411}, "livewire": {"data": {"ues #iOC94uXqUj0ZMf7q9K7r": "array:5 [\n  \"data\" => array:34 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"selection\"\n    \"selectedUesForDuplication\" => []\n    \"targetParcours\" => []\n    \"targetAnneeId\" => \"\"\n    \"duplicationPreview\" => []\n    \"editablePreviewData\" => []\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"\"\n    \"duplicationFilterParcours\" => []\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => false\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"iOC94uXqUj0ZMf7q9K7r\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1071475589 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1071475589\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1854146275 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1854146275\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1077131785 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">iOC94uXqUj0ZMf7q9K7r</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">db60f9bb</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:34</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"9 characters\">selection</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => []\n      \"<span class=sf-dump-key>targetParcours</span>\" => []\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => []\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => []\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => []\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">ceab2766ed7009c81861ad8fd0806d2bb327cb358b4729f5d99938750785e315</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"5 characters\">cadjk</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">openDuplicationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1077131785\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-452424501 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1180</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjA2ODJZSXUwdUV0S1VpcWY5UkVtL1E9PSIsInZhbHVlIjoiL1dVbDVJeW5yZm9ybXlYRkk4bmNjTjg5MFE4TkVBdEl6OHZPeDJpQ1gyWHFHS21xTlRTeVhsVUttcTltZzMxc1YrQTdtRjRuWmF5TitXUGtNYXdQMi92cFJ3Yk94QUFCY3NaSGlUSldyVEkwd0hBU0lXWW44eDUrL1dudTB4SVUiLCJtYWMiOiIxZGRiMzQ2MjU5YTE2NmYwNzQ2ZDJkY2Q3MjZmZjg0N2UyMTY0YmU1N2E1YTMwYzM1ODFjMjMzOWI3MjIxZjgxIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlEyekowbUh5VnBTS043RllOTTBPdHc9PSIsInZhbHVlIjoiOUE3SlRpa1kvc1VVbGJWcFJOb1BtTEFPS1p5UGFsYk5HOVFWOGZYcE4ySngwZnkvMFpzWGh1VnEzandIdHBkWUkrL0pnTUJMOUVNdStwUmZuQU9zT29pZWZmei9rdXRtNmlnYWJwOEM3b09lU2RrSVJNM3dOQk9mV1RxVjRoM08iLCJtYWMiOiJjYTRjZjQ4MGFlZTU0NjgwZjZlZGIwYzgzNmYyYTE1MWJlZWE0ZWEzYWQ2ZjIwYmY2YzA1Yzk2NzJiZGUzNDI3IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-452424501\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1376872975 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">58388</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1180</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">1180</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjA2ODJZSXUwdUV0S1VpcWY5UkVtL1E9PSIsInZhbHVlIjoiL1dVbDVJeW5yZm9ybXlYRkk4bmNjTjg5MFE4TkVBdEl6OHZPeDJpQ1gyWHFHS21xTlRTeVhsVUttcTltZzMxc1YrQTdtRjRuWmF5TitXUGtNYXdQMi92cFJ3Yk94QUFCY3NaSGlUSldyVEkwd0hBU0lXWW44eDUrL1dudTB4SVUiLCJtYWMiOiIxZGRiMzQ2MjU5YTE2NmYwNzQ2ZDJkY2Q3MjZmZjg0N2UyMTY0YmU1N2E1YTMwYzM1ODFjMjMzOWI3MjIxZjgxIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IlEyekowbUh5VnBTS043RllOTTBPdHc9PSIsInZhbHVlIjoiOUE3SlRpa1kvc1VVbGJWcFJOb1BtTEFPS1p5UGFsYk5HOVFWOGZYcE4ySngwZnkvMFpzWGh1VnEzandIdHBkWUkrL0pnTUJMOUVNdStwUmZuQU9zT29pZWZmei9rdXRtNmlnYWJwOEM3b09lU2RrSVJNM3dOQk9mV1RxVjRoM08iLCJtYWMiOiJjYTRjZjQ4MGFlZTU0NjgwZjZlZGIwYzgzNmYyYTE1MWJlZWE0ZWEzYWQ2ZjIwYmY2YzA1Yzk2NzJiZGUzNDI3IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.7398</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1376872975\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-254199192 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-254199192\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1804947625 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 07:42:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6ImlHbm5OYi9yWkc4L3hKclIwZkxWbFE9PSIsInZhbHVlIjoiVWQwMy9lMUtzRWRCREhvaThvOVlRK1owbXBBSWhpR05haUxScWs2UEdrS2xlQVVacmYwem41ZDBlY3BaT2phdVhEa2lxT0RmWnNZd2FVTDVBNUhXMUtocnBmMVlNVXpWRVBneXpOT0IxME12MlNxQ3UrazJtb3hnMjduZE02QkMiLCJtYWMiOiJmZWRjZDA5NzI5NTRhMTVlNjIxYjJkMGIzM2QwZWQ3OTBiZGM1NGIyZDg2ZWIyNzdiNjZhMDUwZjA2YzllNTBhIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:42:59 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImR1VDhwT2VDVGJuK2h0M0d0SGdFdXc9PSIsInZhbHVlIjoiRU1HVkowUkVLRlQ5UytnWDkvck5teVVDOWNCUEM4UysxK1hidEVjMk9NQlhaaUIvWlBjVGllbVhLbkhrYU5lcnNjdklPYmxTNlExbEhPUVhGazJkWmFFcnZ1eGk0YmxPSXlwd3NKSHRCZEkvTDNXRnBUd3Y3V0RQWUZ6VlZNQm4iLCJtYWMiOiJmYmFkZjQwYmMyNTdmMzM1MTFkZGM5OTQxNDdmY2FhODAzYjkzZWRhMGYyNjQxN2VmMzdiNzU5MWFkZDRhYjRlIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 09:42:59 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6ImlHbm5OYi9yWkc4L3hKclIwZkxWbFE9PSIsInZhbHVlIjoiVWQwMy9lMUtzRWRCREhvaThvOVlRK1owbXBBSWhpR05haUxScWs2UEdrS2xlQVVacmYwem41ZDBlY3BaT2phdVhEa2lxT0RmWnNZd2FVTDVBNUhXMUtocnBmMVlNVXpWRVBneXpOT0IxME12MlNxQ3UrazJtb3hnMjduZE02QkMiLCJtYWMiOiJmZWRjZDA5NzI5NTRhMTVlNjIxYjJkMGIzM2QwZWQ3OTBiZGM1NGIyZDg2ZWIyNzdiNjZhMDUwZjA2YzllNTBhIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:42:59 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImR1VDhwT2VDVGJuK2h0M0d0SGdFdXc9PSIsInZhbHVlIjoiRU1HVkowUkVLRlQ5UytnWDkvck5teVVDOWNCUEM4UysxK1hidEVjMk9NQlhaaUIvWlBjVGllbVhLbkhrYU5lcnNjdklPYmxTNlExbEhPUVhGazJkWmFFcnZ1eGk0YmxPSXlwd3NKSHRCZEkvTDNXRnBUd3Y3V0RQWUZ6VlZNQm4iLCJtYWMiOiJmYmFkZjQwYmMyNTdmMzM1MTFkZGM5OTQxNDdmY2FhODAzYjkzZWRhMGYyNjQxN2VmMzdiNzU5MWFkZDRhYjRlIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 09:42:59 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1804947625\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-826085362 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-826085362\", {\"maxDepth\":0})</script>\n"}}