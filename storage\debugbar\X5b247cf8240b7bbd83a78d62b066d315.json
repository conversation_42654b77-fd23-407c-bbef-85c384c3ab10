{"__meta": {"id": "X5b247cf8240b7bbd83a78d62b066d315", "datetime": "2025-09-17 11:27:37", "utime": 1758097657.106858, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758097655.738421, "end": 1758097657.106892, "duration": 1.3684711456298828, "duration_str": "1.37s", "measures": [{"label": "Booting", "start": 1758097655.738421, "relative_start": 0, "end": 1758097656.199658, "relative_end": 1758097656.199658, "duration": 0.46123695373535156, "duration_str": "461ms", "params": [], "collector": null}, {"label": "Application", "start": 1758097656.200626, "relative_start": 0.4622049331665039, "end": 1758097657.106897, "relative_end": 5.0067901611328125e-06, "duration": 0.90627121925354, "duration_str": "906ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27920808, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 45, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 47, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 53, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 28, "nb_failed_statements": 0, "accumulated_duration": 0.06512, "accumulated_duration_str": "65.12ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00733, "duration_str": "7.33ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 11.256}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 684}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:684", "connection": "imsaaapp", "start_percent": 11.256, "width_percent": 0}, {"sql": "select * from `ues` where `ues`.`id` = 301 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["301"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 690}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00156, "duration_str": "1.56ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:690", "connection": "imsaaapp", "start_percent": 11.256, "width_percent": 2.396}, {"sql": "select * from `ues` where `code` = 'IMTHRL11ENJUFI' and `parcour_id` = '3' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTHRL11ENJUFI", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 88}, {"index": 16, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00116, "duration_str": "1.16ms", "stmt_id": "\\app\\Models\\Ue.php:88", "connection": "imsaaapp", "start_percent": 13.652, "width_percent": 1.781}, {"sql": "insert into `ues` (`nom`, `code`, `credit`, `niveau_id`, `semestre_id`, `parcour_id`, `annee_universitaire_id`) values ('ENVIRONNEMENT JURIDIQUE ET FISCAL', 'IMTHRL11ENJUFI', 7, 1, 1, '3', '7')", "type": "query", "params": [], "bindings": ["ENVIRONNEMENT JURIDIQUE ET FISCAL", "IMTHRL11ENJUFI", "7", "1", "1", "3", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.01782, "duration_str": "17.82ms", "stmt_id": "\\app\\Models\\Ue.php:107", "connection": "imsaaapp", "start_percent": 15.433, "width_percent": 27.365}, {"sql": "select * from `matieres` where `matieres`.`ue_id` = 301 and `matieres`.`ue_id` is not null and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["301"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 110}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Models\\Ue.php:110", "connection": "imsaaapp", "start_percent": 42.798, "width_percent": 2.365}, {"sql": "select exists(select * from `matieres` where `code` = 'INIJUR' and `matieres`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["INIJUR"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 165}, {"index": 11, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 12, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 13, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.00107, "duration_str": "1.07ms", "stmt_id": "\\app\\Models\\Ue.php:165", "connection": "imsaaapp", "start_percent": 45.163, "width_percent": 1.643}, {"sql": "select * from `parcours` where `parcours`.`id` = '3' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["3"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 170}, {"index": 22, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 23, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 24, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Models\\Ue.php:170", "connection": "imsaaapp", "start_percent": 46.806, "width_percent": 1.628}, {"sql": "select exists(select * from `matieres` where `code` = 'INIJUR_CM' and `matieres`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["INIJUR_CM"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 173}, {"index": 11, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 12, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 13, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.0007, "duration_str": "700μs", "stmt_id": "\\app\\Models\\Ue.php:173", "connection": "imsaaapp", "start_percent": 48.434, "width_percent": 1.075}, {"sql": "select * from `matieres` where `matieres`.`ue_id` = 472 and `matieres`.`ue_id` is not null and `code` = 'INIJUR_CM' and `matieres`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["472", "INIJUR_CM"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 132}, {"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Models\\Ue.php:132", "connection": "imsaaapp", "start_percent": 49.509, "width_percent": 1.09}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 738}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:738", "connection": "imsaaapp", "start_percent": 50.599, "width_percent": 0}, {"sql": "select * from `ues` where `parcour_id` in ('1') and `semestre_id` in ('1') and `annee_universitaire_id` = '5' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["1", "1", "5"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00521, "duration_str": "5.21ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 50.599, "width_percent": 8.001}, {"sql": "select * from `parcours` where `parcours`.`id` in (1) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 58.6, "width_percent": 1.873}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 60.473, "width_percent": 1.244}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0010500000000000002, "duration_str": "1.05ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 61.717, "width_percent": 1.612}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 63.329, "width_percent": 1.198}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (301, 302, 303, 304) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00106, "duration_str": "1.06ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 64.527, "width_percent": 1.628}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00271, "duration_str": "2.71ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 66.155, "width_percent": 4.162}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00132, "duration_str": "1.32ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 70.316, "width_percent": 2.027}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00122, "duration_str": "1.22ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 72.343, "width_percent": 1.873}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 74.217, "width_percent": 1.259}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00133, "duration_str": "1.33ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 75.476, "width_percent": 2.042}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.001, "duration_str": "1ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 77.518, "width_percent": 1.536}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0015400000000000001, "duration_str": "1.54ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 79.054, "width_percent": 2.365}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00164, "duration_str": "1.64ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 81.419, "width_percent": 2.518}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 83.937, "width_percent": 1.367}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 85.304, "width_percent": 1.213}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 86.517, "width_percent": 1.244}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0032, "duration_str": "3.2ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 87.761, "width_percent": 4.914}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 128}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00477, "duration_str": "4.77ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:128", "connection": "imsaaapp", "start_percent": 92.675, "width_percent": 7.325}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 9, "App\\Models\\Semestre": 12, "App\\Models\\Niveau": 7, "App\\Models\\Parcour": 29, "App\\Models\\Matiere": 31, "App\\Models\\Ue": 15, "App\\Models\\User": 64}, "count": 167}, "livewire": {"data": {"ues #sJT7BchAE2L12UyAchxq": "array:5 [\n  \"data\" => array:35 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"processing\"\n    \"selectedUesForDuplication\" => array:4 [\n      0 => 301\n      1 => 302\n      2 => 303\n      3 => 304\n    ]\n    \"targetParcours\" => array:1 [\n      0 => \"3\"\n    ]\n    \"targetAnneeId\" => \"7\"\n    \"duplicationPreview\" => array:4 [\n      301 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 301\n          \"nom\" => \"ENVIRONNEMENT JURIDIQUE ET FISCAL\"\n          \"code\" => \"IMTHRL11ENJUFI\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 489\n            \"nom\" => \"Initiation juridique\"\n            \"code\" => \"INIJUR\"\n            \"syllabus\" => null\n            \"user_id\" => 152\n            \"enseignant_nom\" => \" Marie ANNE\"\n          ]\n          1 => array:6 [\n            \"id\" => 490\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISCALT\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      302 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 302\n          \"nom\" => \"LANGUE ET COMMUNICATION\"\n          \"code\" => \"IMTHRL11LANCOM\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 491\n            \"nom\" => \"Communication d'entreprise\"\n            \"code\" => \"COMENTR\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 492\n            \"nom\" => \"Italien\"\n            \"code\" => \"ITALIE\"\n            \"syllabus\" => null\n            \"user_id\" => 180\n            \"enseignant_nom\" => \" ANDREAS\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      303 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 303\n          \"nom\" => \"GESTION ET COMPTABILITÉ\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 493\n            \"nom\" => \"Organisation\"\n            \"code\" => \"ORGAN\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 494\n            \"nom\" => \"Comptabilité \"\n            \"code\" => \"COMPTAGENL\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n      304 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 304\n          \"nom\" => \"INSERTION PROFESSIONNELLE\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 495\n            \"nom\" => \"Visite d'entreprise\"\n            \"code\" => \"VISITEENTRE\"\n            \"syllabus\" => null\n            \"user_id\" => 147\n            \"enseignant_nom\" => \"RAMINISOA Jerry Léoness\"\n          ]\n          1 => array:6 [\n            \"id\" => 496\n            \"nom\" => \"Informatique\"\n            \"code\" => \"INFORMA\"\n            \"syllabus\" => null\n            \"user_id\" => 145\n            \"enseignant_nom\" => \"RANAIVOSOA Edwino\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 3\n            \"nom\" => \"Communication et Marketing\"\n            \"sigle\" => \"CM\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n    ]\n    \"editablePreviewData\" => array:4 [\n      301 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 301\n          \"nom\" => \"ENVIRONNEMENT JURIDIQUE ET FISCAL\"\n          \"code\" => \"IMTHRL11ENJUFI\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 489\n            \"nom\" => \"Initiation juridique\"\n            \"code\" => \"INIJUR\"\n            \"syllabus\" => null\n            \"user_id\" => 152\n            \"enseignant_nom\" => \" Marie ANNE\"\n          ]\n          1 => array:6 [\n            \"id\" => 490\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISCALT\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n      302 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 302\n          \"nom\" => \"LANGUE ET COMMUNICATION\"\n          \"code\" => \"IMTHRL11LANCOM\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 491\n            \"nom\" => \"Communication d'entreprise\"\n            \"code\" => \"COMENTR\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 492\n            \"nom\" => \"Italien\"\n            \"code\" => \"ITALIE\"\n            \"syllabus\" => null\n            \"user_id\" => 180\n            \"enseignant_nom\" => \" ANDREAS\"\n          ]\n        ]\n      ]\n      303 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 303\n          \"nom\" => \"GESTION ET COMPTABILITÉ\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 493\n            \"nom\" => \"Organisation\"\n            \"code\" => \"ORGAN\"\n            \"syllabus\" => null\n            \"user_id\" => 148\n            \"enseignant_nom\" => \" LOKO\"\n          ]\n          1 => array:6 [\n            \"id\" => 494\n            \"nom\" => \"Comptabilité \"\n            \"code\" => \"COMPTAGENL\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n      304 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 304\n          \"nom\" => \"INSERTION PROFESSIONNELLE\"\n          \"code\" => \"IMTHRL11\"\n          \"credit\" => 8\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 495\n            \"nom\" => \"Visite d'entreprise\"\n            \"code\" => \"VISITEENTRE\"\n            \"syllabus\" => null\n            \"user_id\" => 147\n            \"enseignant_nom\" => \"RAMINISOA Jerry Léoness\"\n          ]\n          1 => array:6 [\n            \"id\" => 496\n            \"nom\" => \"Informatique\"\n            \"code\" => \"INFORMA\"\n            \"syllabus\" => null\n            \"user_id\" => 145\n            \"enseignant_nom\" => \"RANAIVOSOA Edwino\"\n          ]\n        ]\n      ]\n    ]\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"5\"\n    \"duplicationFilterParcours\" => array:1 [\n      0 => \"1\"\n    ]\n    \"duplicationFilterSemestres\" => array:1 [\n      0 => \"1\"\n    ]\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => true\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"sJT7BchAE2L12UyAchxq\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1006622187 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1006622187\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-41892392 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-41892392\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-266369752 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">sJT7BchAE2L12UyAchxq</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">764f0775</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:35</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"7 characters\">preview</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-num>301</span>\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-num>302</span>\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-num>303</span>\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-num>304</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>3</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>301</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>301</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"33 characters\">ENVIRONNEMENT JURIDIQUE ET FISCAL</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">IMTHRL11ENJUFI</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>489</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Initiation juridique</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">INIJUR</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>152</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"11 characters\"> Marie ANNE</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>490</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">FISCALT</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication et Marketing</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"2 characters\">CM</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n        <span class=sf-dump-key>302</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>302</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">LANGUE ET COMMUNICATION</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">IMTHRL11LANCOM</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>8</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>491</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication d&#039;entreprise</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">COMENTR</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>148</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\"> LOKO</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>492</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Italien</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ITALIE</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>180</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> ANDREAS</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication et Marketing</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"2 characters\">CM</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n        <span class=sf-dump-key>303</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>303</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">GESTION ET COMPTABILIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IMTHRL11</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>493</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Organisation</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ORGAN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>148</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\"> LOKO</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>494</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Comptabilit&#233; </span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">COMPTAGENL</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication et Marketing</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"2 characters\">CM</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n        <span class=sf-dump-key>304</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>304</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"25 characters\">INSERTION PROFESSIONNELLE</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IMTHRL11</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>8</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>495</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Visite d&#039;entreprise</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">VISITEENTRE</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>147</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">RAMINISOA Jerry L&#233;oness</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>496</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Informatique</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">INFORMA</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>145</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"17 characters\">RANAIVOSOA Edwino</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication et Marketing</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"2 characters\">CM</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => <span class=sf-dump-note>array:4</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>301</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>301</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"33 characters\">ENVIRONNEMENT JURIDIQUE ET FISCAL</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">IMTHRL11ENJUFI</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>489</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Initiation juridique</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">INIJUR</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>152</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"11 characters\"> Marie ANNE</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>490</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">FISCALT</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        <span class=sf-dump-key>302</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>302</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">LANGUE ET COMMUNICATION</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"14 characters\">IMTHRL11LANCOM</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>8</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>491</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"26 characters\">Communication d&#039;entreprise</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">COMENTR</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>148</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\"> LOKO</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>492</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Italien</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"6 characters\">ITALIE</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>180</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> ANDREAS</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        <span class=sf-dump-key>303</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>303</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">GESTION ET COMPTABILIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IMTHRL11</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>493</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Organisation</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"5 characters\">ORGAN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>148</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"5 characters\"> LOKO</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>494</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Comptabilit&#233; </span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">COMPTAGENL</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n        <span class=sf-dump-key>304</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>304</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"25 characters\">INSERTION PROFESSIONNELLE</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"8 characters\">IMTHRL11</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>8</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>495</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Visite d&#039;entreprise</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"11 characters\">VISITEENTRE</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>147</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"23 characters\">RAMINISOA Jerry L&#233;oness</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>496</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"12 characters\">Informatique</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">INFORMA</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>145</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"17 characters\">RANAIVOSOA Edwino</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"<span class=sf-dump-str>5</span>\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>duplicationFilterSemestres</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">5b4b73661c95fda30bcc5a41eda93f9600084d62ac017219dd2755c6e0dfabb9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">g8qv</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"18 characters\">executeDuplication</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266369752\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-767238396 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">4889</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Impoa3NPaGhMM0NPUkxQVTdBQkRhbEE9PSIsInZhbHVlIjoiMjFLcDlNMjVxbG9JUGltQmJXYTVHQVlqMTZwTVo3R2krZUNlOTZxdDN2YTFEcmRUN0p2Lzk2UEE0ZFZ0ZHVMUndUS3BFOHQ4NEx4UEwzbWtNOU16QTJvckFiQUhjM2tlTDlPUmFzL3ZBYzZrUGR3VnRLbHVmZ2pmTW90YnUwb3oiLCJtYWMiOiJiMmQxMDdmZTA4ZGJlYmZiN2FlNmJlNTRkMzM0ODUzODMwYmEyYWM1MDUwNjU0NTQzMTQwZjFhN2ViYTBiNTU0IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjJ1NnY3M3FUQXE4T25McE85RFBjMnc9PSIsInZhbHVlIjoiZHZhK1ovZitsdFdRSi9CTTZaN1hHaHZJN0g5dDdtbDVEQ1ozVk9oUmdvRGo2Y0NGNjZPUGRuSzJrdDhpdklHNWpoT281blg2dWFESDBTRWo0amcwZkNsT0NzNHVOd2J4dktrV2tWbU9xMk9IVS9OKzNGTnVXV0JRNlZXZXRxRG8iLCJtYWMiOiI4OTIzNTczN2U0ZDI3OTgyZTk5ZDk5ZTY3ZWVjM2RiZDAzMmJiMGE0ZjE0ZTZjZTQ5ZGRjNmRkODMyYTUwY2JhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-767238396\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-959231611 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60539</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4889</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">4889</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Impoa3NPaGhMM0NPUkxQVTdBQkRhbEE9PSIsInZhbHVlIjoiMjFLcDlNMjVxbG9JUGltQmJXYTVHQVlqMTZwTVo3R2krZUNlOTZxdDN2YTFEcmRUN0p2Lzk2UEE0ZFZ0ZHVMUndUS3BFOHQ4NEx4UEwzbWtNOU16QTJvckFiQUhjM2tlTDlPUmFzL3ZBYzZrUGR3VnRLbHVmZ2pmTW90YnUwb3oiLCJtYWMiOiJiMmQxMDdmZTA4ZGJlYmZiN2FlNmJlNTRkMzM0ODUzODMwYmEyYWM1MDUwNjU0NTQzMTQwZjFhN2ViYTBiNTU0IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjJ1NnY3M3FUQXE4T25McE85RFBjMnc9PSIsInZhbHVlIjoiZHZhK1ovZitsdFdRSi9CTTZaN1hHaHZJN0g5dDdtbDVEQ1ozVk9oUmdvRGo2Y0NGNjZPUGRuSzJrdDhpdklHNWpoT281blg2dWFESDBTRWo0amcwZkNsT0NzNHVOd2J4dktrV2tWbU9xMk9IVS9OKzNGTnVXV0JRNlZXZXRxRG8iLCJtYWMiOiI4OTIzNTczN2U0ZDI3OTgyZTk5ZDk5ZTY3ZWVjM2RiZDAzMmJiMGE0ZjE0ZTZjZTQ5ZGRjNmRkODMyYTUwY2JhIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758097655.7384</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758097655</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-959231611\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-428382282 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-428382282\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 08:27:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlNwZlA2MVBZekhlTFIvUzNDVitZcGc9PSIsInZhbHVlIjoieHkzSmVYSmtZWENhSi9NQmJ2YkkzRWxaeFAwNUxDQjltdjBIMnB4WHdxM0xHRXRmcUs3Uk5ZRC9XN1VjUWhUeVg1NVZvVVZPMmJUNWVEVEtrNGVoUFlNOVpRVnhscnE5ZjNleWRNZERxZ1JXRjgrRmNENHlONkpsaTM2NFM4S3oiLCJtYWMiOiJjMmI2N2JjZmUyOWRhM2ZmYzMwZmY2ZDQxZTA3ZWFjOTA0MDA0NjkzZDYyMTkwNDM0MjkwOTdmZDk0ZTg5NzI3IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:27:37 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6IlBXajlQWDhtVExqWldwNGVRUXo0a1E9PSIsInZhbHVlIjoiZ0lPbEh3VTZDdHNLWkdaODByT3RXMDRVWGdPcDBGeDlKWGxML3V1aENwM3lwUTNtdWpzUWcvVElyQlkwTXg0U1A1Z0xMRHVPUGRwOUtIRng4MFlUZVdBQ01DUVZWTEtXVVArQjhWVzdQbG51K3R5TW40SFhoZFBNVUNWVUsxaDkiLCJtYWMiOiJmZTZjNzE3ZDI3OTM1M2I4Yjk1N2E3ZGIzNjk5YmRkMjdlM2Q4ZjM1MDJmMmQwNjc3MjllOGMyMzY4MzA0YTE1IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:27:37 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlNwZlA2MVBZekhlTFIvUzNDVitZcGc9PSIsInZhbHVlIjoieHkzSmVYSmtZWENhSi9NQmJ2YkkzRWxaeFAwNUxDQjltdjBIMnB4WHdxM0xHRXRmcUs3Uk5ZRC9XN1VjUWhUeVg1NVZvVVZPMmJUNWVEVEtrNGVoUFlNOVpRVnhscnE5ZjNleWRNZERxZ1JXRjgrRmNENHlONkpsaTM2NFM4S3oiLCJtYWMiOiJjMmI2N2JjZmUyOWRhM2ZmYzMwZmY2ZDQxZTA3ZWFjOTA0MDA0NjkzZDYyMTkwNDM0MjkwOTdmZDk0ZTg5NzI3IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:27:37 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6IlBXajlQWDhtVExqWldwNGVRUXo0a1E9PSIsInZhbHVlIjoiZ0lPbEh3VTZDdHNLWkdaODByT3RXMDRVWGdPcDBGeDlKWGxML3V1aENwM3lwUTNtdWpzUWcvVElyQlkwTXg0U1A1Z0xMRHVPUGRwOUtIRng4MFlUZVdBQ01DUVZWTEtXVVArQjhWVzdQbG51K3R5TW40SFhoZFBNVUNWVUsxaDkiLCJtYWMiOiJmZTZjNzE3ZDI3OTM1M2I4Yjk1N2E3ZGIzNjk5YmRkMjdlM2Q4ZjM1MDJmMmQwNjc3MjllOGMyMzY4MzA0YTE1IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:27:37 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-25044095 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-25044095\", {\"maxDepth\":0})</script>\n"}}