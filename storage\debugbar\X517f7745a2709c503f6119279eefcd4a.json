{"__meta": {"id": "X517f7745a2709c503f6119279eefcd4a", "datetime": "2025-09-17 11:32:01", "utime": 1758097921.515315, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758097920.286942, "end": 1758097921.515346, "duration": 1.2284040451049805, "duration_str": "1.23s", "measures": [{"label": "Booting", "start": 1758097920.286942, "relative_start": 0, "end": 1758097920.822625, "relative_end": 1758097920.822625, "duration": 0.5356829166412354, "duration_str": "536ms", "params": [], "collector": null}, {"label": "Application", "start": 1758097920.824906, "relative_start": 0.5379641056060791, "end": 1758097921.51535, "relative_end": 4.0531158447265625e-06, "duration": 0.6904439926147461, "duration_str": "690ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 28462872, "peak_usage_str": "27MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 45, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 47, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 53, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 28, "nb_failed_statements": 0, "accumulated_duration": 0.07075, "accumulated_duration_str": "70.75ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00763, "duration_str": "7.63ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 10.784}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 684}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:684", "connection": "imsaaapp", "start_percent": 10.784, "width_percent": 0}, {"sql": "select * from `ues` where `ues`.`id` = 80 and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["80"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 690}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:690", "connection": "imsaaapp", "start_percent": 10.784, "width_percent": 1.117}, {"sql": "select * from `ues` where `code` = 'IMTBL11EJF' and `parcour_id` = '1' and `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["IMTBL11EJF", "1", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 88}, {"index": 16, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00138, "duration_str": "1.38ms", "stmt_id": "\\app\\Models\\Ue.php:88", "connection": "imsaaapp", "start_percent": 11.901, "width_percent": 1.951}, {"sql": "insert into `ues` (`nom`, `code`, `credit`, `niveau_id`, `semestre_id`, `parcour_id`, `annee_universitaire_id`) values ('ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALITÉ', 'IMTBL11EJF', 7, 1, 1, '1', '7')", "type": "query", "params": [], "bindings": ["ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&Eacute;", "IMTBL11EJF", "7", "1", "1", "1", "7"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 107}, {"index": 21, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\app\\Models\\Ue.php:107", "connection": "imsaaapp", "start_percent": 13.852, "width_percent": 2.247}, {"sql": "select * from `matieres` where `matieres`.`ue_id` = 80 and `matieres`.`ue_id` is not null and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["80"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 110}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Models\\Ue.php:110", "connection": "imsaaapp", "start_percent": 16.099, "width_percent": 1.159}, {"sql": "select exists(select * from `matieres` where `code` = 'COMPTAGEN' and `matieres`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["COMPTAGEN"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 165}, {"index": 11, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 12, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 13, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.00256, "duration_str": "2.56ms", "stmt_id": "\\app\\Models\\Ue.php:165", "connection": "imsaaapp", "start_percent": 17.258, "width_percent": 3.618}, {"sql": "select * from `parcours` where `parcours`.`id` = '1' and `parcours`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 170}, {"index": 22, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 23, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 24, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Models\\Ue.php:170", "connection": "imsaaapp", "start_percent": 20.876, "width_percent": 1.004}, {"sql": "select exists(select * from `matieres` where `code` = 'COMPTAGEN_THR' and `matieres`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["COMPTAGEN_THR"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 173}, {"index": 11, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 129}, {"index": 12, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 13, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}], "duration": 0.00082, "duration_str": "820μs", "stmt_id": "\\app\\Models\\Ue.php:173", "connection": "imsaaapp", "start_percent": 21.88, "width_percent": 1.159}, {"sql": "select * from `matieres` where `matieres`.`ue_id` = 473 and `matieres`.`ue_id` is not null and `code` = 'COMPTAGEN_THR' and `matieres`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["473", "COMPTAGEN_THR"], "hints": null, "show_copy": false, "backtrace": [{"index": 18, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 132}, {"index": 19, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 111}, {"index": 20, "namespace": null, "name": "\\app\\Models\\Ue.php", "line": 64}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 706}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00073, "duration_str": "730μs", "stmt_id": "\\app\\Models\\Ue.php:132", "connection": "imsaaapp", "start_percent": 23.039, "width_percent": 1.032}, {"sql": "Rollback Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 8, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 738}, {"index": 9, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}, {"index": 13, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\HandlesActions.php", "line": 149}, {"index": 14, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\HydrationMiddleware\\PerformActionCalls.php", "line": 36}, {"index": 15, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\LifecycleManager.php", "line": 89}, {"index": 16, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Connection\\ConnectionHandler.php", "line": 13}, {"index": 17, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\Controllers\\HttpConnectionHandler.php", "line": 18}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 46}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 798}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 50}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php", "line": 78}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php", "line": 49}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 121}, {"index": 30, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 64}, {"index": 31, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 32, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php", "line": 37}, {"index": 33, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 34, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php", "line": 67}, {"index": 35, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 36, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 116}, {"index": 37, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 797}, {"index": 38, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 776}, {"index": 39, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 740}, {"index": 40, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 729}, {"index": 41, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php", "line": 190}, {"index": 42, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 141}, {"index": 43, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\DisableBrowserCache.php", "line": 19}, {"index": 44, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 45, "namespace": null, "name": "\\vendor\\beyondcode\\laravel-query-detector\\src\\QueryDetectorMiddleware.php", "line": 33}, {"index": 46, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 48, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 180}, {"index": 49, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php", "line": 27}], "duration": 0, "duration_str": "", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:738", "connection": "imsaaapp", "start_percent": 24.071, "width_percent": 0}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0035499999999999998, "duration_str": "3.55ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 24.071, "width_percent": 5.018}, {"sql": "select * from `parcours` where `parcours`.`id` in (1, 2, 3, 4, 5, 6, 7, 8, 10, 15, 18, 24) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00087, "duration_str": "870μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 29.088, "width_percent": 1.23}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1, 2, 3) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00076, "duration_str": "760μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 30.318, "width_percent": 1.074}, {"sql": "select * from `semestres` where `semestres`.`id` in (1, 2, 3, 4, 5, 6, 8) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00091, "duration_str": "910μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 31.392, "width_percent": 1.286}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (3, 4, 5) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.0020800000000000003, "duration_str": "2.08ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 32.678, "width_percent": 2.94}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133, 134, 135, 136, 137, 138, 139, 140, 141, 143, 144, 145, 146, 147, 148, 149, 150, 151, 152, 153, 154, 155, 156, 157, 158, 159, 160, 161, 162, 163, 164, 165, 166, 167, 168, 169, 170, 171, 172, 173, 174, 175, 176, 177, 178, 179, 180, 181, 182, 183, 184, 185, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 196, 197, 198, 199, 200, 201, 202, 203, 204, 205, 206, 207, 208, 209, 210, 211, 212, 213, 214, 216, 217, 218, 219, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 233, 234, 235, 236, 237, 238, 239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 262, 263, 264, 265, 276, 277, 278, 280, 282, 283, 286, 287, 288, 289, 290, 291, 292, 293, 294, 295, 296, 297, 298, 299, 300, 301, 302, 303, 304, 305, 306, 307, 308, 309, 310, 311, 312, 313, 314, 315, 316, 317, 318, 319, 320, 321, 322, 323, 324, 325, 326, 327, 328, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342, 343, 344, 345, 346, 347, 348, 349, 350, 351, 353, 354, 355, 356, 358, 359, 360, 361, 363, 364, 365, 366, 367, 368, 369, 370, 371, 372, 373, 374, 375, 376, 377, 378, 379, 380, 381, 382, 383, 384, 385, 386, 387, 388, 389, 390, 391, 392, 393, 394, 395, 396, 397, 398, 399, 400, 401, 402, 403, 404, 405, 406, 407, 408, 409, 410, 411, 412, 413, 414, 415, 416, 417, 418, 419, 420, 421, 422, 423, 424, 425, 426, 427, 428, 429, 430, 431, 432, 433, 434, 435, 436, 437, 438, 439, 440, 441, 442, 443, 444, 445, 446, 447, 448, 449, 450, 451, 452, 453, 454, 455, 456, 457, 458, 459, 460, 461, 462, 463, 464, 465, 466, 467) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 574}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 528}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 119}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}], "duration": 0.00887, "duration_str": "8.87ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:574", "connection": "imsaaapp", "start_percent": 35.618, "width_percent": 12.537}, {"sql": "select count(*) as aggregate from `ues` where `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00335, "duration_str": "3.35ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 48.155, "width_percent": 4.735}, {"sql": "select * from `ues` where `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00523, "duration_str": "5.23ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 52.89, "width_percent": 7.392}, {"sql": "select * from `parcours` where `parcours`.`id` in (2, 4, 5) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0028, "duration_str": "2.8ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 60.283, "width_percent": 3.958}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00102, "duration_str": "1.02ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 64.24, "width_percent": 1.442}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0013, "duration_str": "1.3ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 65.682, "width_percent": 1.837}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (4) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00136, "duration_str": "1.36ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 67.519, "width_percent": 1.922}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (80, 84, 85, 87, 88, 89, 90, 91, 92, 93) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00439, "duration_str": "4.39ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 69.442, "width_percent": 6.205}, {"sql": "select * from `users` where `users`.`id` in (145, 147, 148, 149, 151, 155, 156, 157, 158, 159, 177, 180) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00261, "duration_str": "2.61ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 75.647, "width_percent": 3.689}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00127, "duration_str": "1.27ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 79.336, "width_percent": 1.795}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0029100000000000003, "duration_str": "2.91ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 81.131, "width_percent": 4.113}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0022, "duration_str": "2.2ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 85.244, "width_percent": 3.11}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00225, "duration_str": "2.25ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 88.353, "width_percent": 3.18}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 128}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0059900000000000005, "duration_str": "5.99ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:128", "connection": "imsaaapp", "start_percent": 91.534, "width_percent": 8.466}]}, "models": {"data": {"App\\Models\\AnneeUniversitaire": 11, "App\\Models\\Semestre": 18, "App\\Models\\Niveau": 9, "App\\Models\\Parcour": 40, "App\\Models\\Matiere": 767, "App\\Models\\Ue": 374, "App\\Models\\User": 64}, "count": 1283}, "livewire": {"data": {"ues #JQvfaWL1WPJzqAXDz986": "array:5 [\n  \"data\" => array:35 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => true\n    \"duplicationStep\" => \"processing\"\n    \"selectedUesForDuplication\" => array:1 [\n      0 => 80\n    ]\n    \"targetParcours\" => array:1 [\n      0 => \"1\"\n    ]\n    \"targetAnneeId\" => \"7\"\n    \"duplicationPreview\" => array:1 [\n      80 => array:4 [\n        \"ue\" => array:8 [\n          \"id\" => 80\n          \"nom\" => \"ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALITÉ\"\n          \"code\" => \"IMTBL11EJF\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 3\n            \"nom\" => \"Comptabilité Générale\"\n            \"code\" => \"COMPTAGEN\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n          1 => array:6 [\n            \"id\" => 4\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISC\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n        \"target_parcours\" => array:1 [\n          0 => array:3 [\n            \"id\" => 1\n            \"nom\" => \"Tourisme Hôtellerie et Restauration\"\n            \"sigle\" => \"THR\"\n          ]\n        ]\n        \"conflicts\" => []\n      ]\n    ]\n    \"editablePreviewData\" => array:1 [\n      80 => array:2 [\n        \"ue\" => array:8 [\n          \"id\" => 80\n          \"nom\" => \"ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALITÉ\"\n          \"code\" => \"IMTBL11EJF\"\n          \"credit\" => 7\n          \"niveau_id\" => 1\n          \"semestre_id\" => 1\n          \"niveau_nom\" => \"1ère année\"\n          \"semestre_nom\" => \"Semestre 1\"\n        ]\n        \"ecs\" => array:2 [\n          0 => array:6 [\n            \"id\" => 3\n            \"nom\" => \"Comptabilité Générale\"\n            \"code\" => \"COMPTAGEN\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n          1 => array:6 [\n            \"id\" => 4\n            \"nom\" => \"Fiscalité\"\n            \"code\" => \"FISC\"\n            \"syllabus\" => null\n            \"user_id\" => 149\n            \"enseignant_nom\" => \" PARFAIT\"\n          ]\n        ]\n      ]\n    ]\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"\"\n    \"duplicationFilterParcours\" => []\n    \"duplicationFilterSemestres\" => []\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => false\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"JQvfaWL1WPJzqAXDz986\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-839605962 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-839605962\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-172100529 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-172100529\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1003496091 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">JQvfaWL1WPJzqAXDz986</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">712a42b6</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:35</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"7 characters\">preview</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-num>80</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">COMPTAGEN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FISC</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Tourisme H&#244;tellerie et Restauration</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"3 characters\">THR</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">COMPTAGEN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FISC</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>duplicationResults</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterSemestres</span>\" => []\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">aabf65ac61dcb53c186dcff0dbd2266129eb801827a132e812cb8b91f0e53260</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"3 characters\">xkt</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"18 characters\">executeDuplication</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003496091\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2077030199 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2158</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNYRXM3MHNJUnZMSFBUNDNsQis3N0E9PSIsInZhbHVlIjoiajJiQWZ0NzhZQkNQdDBidjhVN0ViL09qdVIxYVlTT0lWbE5CM0tSeGJMZkZvSWNlMlZSVURsZE96ZlR5VWNqck9kQXYxZnpzZzJqNGt2eVM1dzVQNlN3MTlKY3dXSjVzYjlmV0xyMUdqaUtJNGhOUHdsNGNSNlpNRVNRL2FIUWkiLCJtYWMiOiJiZTZiZDZiNWVkOGI2MmMxYzMzMzIzNGFiZTAzODJjNGFlN2M3NTgzYTMxZWRhMjNjZWRjNzk4ODE0ZTFhNjI3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjFvUmNkMEFTNFpLb3MrK0QvNm1UeHc9PSIsInZhbHVlIjoiWlBVYWloZUlBZTIyVVZYQXJJazBzNmo0TE5Ld3NRN3U1OHN1TXRBdnlIVmJTQi9FRUNLT3Q3cDM1WmttaE03R1hkQVY0dWZGdG5XOWY2TW1oc1ZISk9sdnFqTU1vVUwzazBDdDhjMXpCSDRoa3dYSEFnVWFFRm1TNGZuQndKcFgiLCJtYWMiOiJmYjczYzYzZjBkMjJiNDIzMjcwOWM2NzZkOTJlMmNkZTM3ODI4MzQ2OTFkNjMzMGY5Y2UzMGJkZGFjYjBmODA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2077030199\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1971359964 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60766</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2158</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2158</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6InNYRXM3MHNJUnZMSFBUNDNsQis3N0E9PSIsInZhbHVlIjoiajJiQWZ0NzhZQkNQdDBidjhVN0ViL09qdVIxYVlTT0lWbE5CM0tSeGJMZkZvSWNlMlZSVURsZE96ZlR5VWNqck9kQXYxZnpzZzJqNGt2eVM1dzVQNlN3MTlKY3dXSjVzYjlmV0xyMUdqaUtJNGhOUHdsNGNSNlpNRVNRL2FIUWkiLCJtYWMiOiJiZTZiZDZiNWVkOGI2MmMxYzMzMzIzNGFiZTAzODJjNGFlN2M3NTgzYTMxZWRhMjNjZWRjNzk4ODE0ZTFhNjI3IiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjFvUmNkMEFTNFpLb3MrK0QvNm1UeHc9PSIsInZhbHVlIjoiWlBVYWloZUlBZTIyVVZYQXJJazBzNmo0TE5Ld3NRN3U1OHN1TXRBdnlIVmJTQi9FRUNLT3Q3cDM1WmttaE03R1hkQVY0dWZGdG5XOWY2TW1oc1ZISk9sdnFqTU1vVUwzazBDdDhjMXpCSDRoa3dYSEFnVWFFRm1TNGZuQndKcFgiLCJtYWMiOiJmYjczYzYzZjBkMjJiNDIzMjcwOWM2NzZkOTJlMmNkZTM3ODI4MzQ2OTFkNjMzMGY5Y2UzMGJkZGFjYjBmODA0IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758097920.2869</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758097920</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971359964\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1004814636 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1004814636\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1429070152 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 08:32:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IitPMUJZWE1JVG5vY25ZTVplVWJtY3c9PSIsInZhbHVlIjoiWC8rOHkvSkh0YUJmVWo1blhJMCtLTUhhUHRVQU1SME1pbGdyQ29UMkRpR0owTGNTeGR0RGY5SkdzczU5T1BkTmUxNi80UVNxdzdscWtDd3Zvbkw5a0xoMDF0NWZNcXBFY0xUQzUyWmk4RjdMMmNmM2t3eExaczhkc2I4ZTVpa0siLCJtYWMiOiJhNzNiYTFlZWJjN2U3NTgwYzQ5NTM5Yjk4ZWQxNjQ2NzRhZTY4OTIwMjQ4YTdlODRlYWZmNzgzYzQ1NzVmNmJmIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:32:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6Iit5c1VJK1hIVEJua3ViOGpuYjFTWWc9PSIsInZhbHVlIjoiblR5STRXYWlXUjZldnVBYS93b0JMZUg0RUlXT0hDVlZsYlNPQzBkMXJnVlQwdW1MWDh6dXdVa3hEdGgyNFZOdkRLQ1htQTRJcTI0b1RlQUp3TTUwbExzUEpEWEV5V25MT3dBWGNHQ3ZpRTBrTmNuTTU2ZDBuV2h5K3NMTlY2SGMiLCJtYWMiOiI5Yzk4OTViMWM4NmM4MzNmMzkxNWIyNmFmNGQ4ZDlkMTQ4ZTZmZDZhYzAyZTU4ZTc1NGVkMzY1MDQwNzFhZTRjIiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:32:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IitPMUJZWE1JVG5vY25ZTVplVWJtY3c9PSIsInZhbHVlIjoiWC8rOHkvSkh0YUJmVWo1blhJMCtLTUhhUHRVQU1SME1pbGdyQ29UMkRpR0owTGNTeGR0RGY5SkdzczU5T1BkTmUxNi80UVNxdzdscWtDd3Zvbkw5a0xoMDF0NWZNcXBFY0xUQzUyWmk4RjdMMmNmM2t3eExaczhkc2I4ZTVpa0siLCJtYWMiOiJhNzNiYTFlZWJjN2U3NTgwYzQ5NTM5Yjk4ZWQxNjQ2NzRhZTY4OTIwMjQ4YTdlODRlYWZmNzgzYzQ1NzVmNmJmIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:32:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6Iit5c1VJK1hIVEJua3ViOGpuYjFTWWc9PSIsInZhbHVlIjoiblR5STRXYWlXUjZldnVBYS93b0JMZUg0RUlXT0hDVlZsYlNPQzBkMXJnVlQwdW1MWDh6dXdVa3hEdGgyNFZOdkRLQ1htQTRJcTI0b1RlQUp3TTUwbExzUEpEWEV5V25MT3dBWGNHQ3ZpRTBrTmNuTTU2ZDBuV2h5K3NMTlY2SGMiLCJtYWMiOiI5Yzk4OTViMWM4NmM4MzNmMzkxNWIyNmFmNGQ4ZDlkMTQ4ZTZmZDZhYzAyZTU4ZTc1NGVkMzY1MDQwNzFhZTRjIiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:32:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1429070152\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1576029069 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1576029069\", {\"maxDepth\":0})</script>\n"}}