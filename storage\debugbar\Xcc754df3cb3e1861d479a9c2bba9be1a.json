{"__meta": {"id": "Xcc754df3cb3e1861d479a9c2bba9be1a", "datetime": "2025-09-17 11:37:58", "utime": 1758098278.30006, "method": "POST", "uri": "/livewire/message/ues", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1758098277.568393, "end": 1758098278.300095, "duration": 0.7317020893096924, "duration_str": "732ms", "measures": [{"label": "Booting", "start": 1758098277.568393, "relative_start": 0, "end": 1758098278.019417, "relative_end": 1758098278.019417, "duration": 0.45102405548095703, "duration_str": "451ms", "params": [], "collector": null}, {"label": "Application", "start": 1758098278.020247, "relative_start": 0.45185399055480957, "end": 1758098278.300098, "relative_end": 2.86102294921875e-06, "duration": 0.27985095977783203, "duration_str": "280ms", "params": [], "collector": null}]}, "memory": {"peak_usage": 27287704, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 4, "templates": [{"name": "livewire.deraq.ue.index (\\resources\\views\\livewire\\deraq\\ue\\index.blade.php)", "param_count": 45, "params": ["ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "errors", "_instance", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/index.blade.php&line=0"}, {"name": "livewire.deraq.ue.liste (\\resources\\views\\livewire\\deraq\\ue\\liste.blade.php)", "param_count": 47, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/liste.blade.php&line=0"}, {"name": "livewire::bootstrap (\\vendor\\livewire\\livewire\\src\\views\\pagination\\bootstrap.blade.php)", "param_count": 2, "params": ["paginator", "elements"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\vendor\\livewire\\livewire\\src\\views\\pagination/bootstrap.blade.php&line=0"}, {"name": "livewire.deraq.ue.duplication-modal (\\resources\\views\\livewire\\deraq\\ue\\duplication-modal.blade.php)", "param_count": 53, "params": ["__env", "app", "errors", "_instance", "ues", "parcours", "semestres", "annees", "niveaux", "enseignants", "activeFiltersCount", "livewireLayout", "currentPage", "query", "selectedParcours", "<PERSON><PERSON><PERSON><PERSON>", "filtreAnnee", "newUe", "editUe", "editingUeId", "expandedUes", "showQuickAddModal", "showEcModal", "ecModalMode", "ecForm", "currentEcUe", "currentEcId", "showDuplicationModal", "duplicationStep", "selectedUesForDuplication", "targetParcours", "targetAnneeId", "duplicationPreview", "editablePreviewData", "duplicationResults", "duplicationFilterAnnee", "duplicationFilterParcours", "duplicationFilterSemestres", "duplication<PERSON><PERSON>y", "selectAllUes", "enseignant<PERSON><PERSON><PERSON>", "filteredEnseignants", "showAddEnseignantForm", "newEnseignant", "recentlyAssignedEnseignants", "page", "paginators", "__currentLoopData", "parcour", "loop", "niveau", "annee", "ue"], "type": "blade", "editorLink": "phpstorm://open?file=C:\\xampp\\htdocs\\ImsaaProject\\resources\\views/livewire/deraq/ue/duplication-modal.blade.php&line=0"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 14, "nb_failed_statements": 0, "accumulated_duration": 0.02298, "accumulated_duration_str": "22.98ms", "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00662, "duration_str": "6.62ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:59", "connection": "imsaaapp", "start_percent": 0, "width_percent": 28.808}, {"sql": "select count(*) as aggregate from `ues` where `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 28.808, "width_percent": 4.134}, {"sql": "select * from `ues` where `annee_universitaire_id` = '7' and `ues`.`deleted_at` is null limit 10 offset 0", "type": "query", "params": [], "bindings": ["7"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 32.942, "width_percent": 3.438}, {"sql": "select * from `parcours` where `parcours`.`id` in (1) and `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 36.379, "width_percent": 5.875}, {"sql": "select * from `niveaux` where `niveaux`.`id` in (1) and `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 42.254, "width_percent": 3.133}, {"sql": "select * from `semestres` where `semestres`.`id` in (1) and `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00099, "duration_str": "990μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 45.387, "width_percent": 4.308}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`id` in (7) and `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00067, "duration_str": "670μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 49.695, "width_percent": 2.916}, {"sql": "select * from `matieres` where `matieres`.`ue_id` in (474) and `matieres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 21, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00079, "duration_str": "790μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 52.611, "width_percent": 3.438}, {"sql": "select * from `users` where `users`.`id` in (149) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 123}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 28, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 29, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009, "duration_str": "900μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:123", "connection": "imsaaapp", "start_percent": 56.049, "width_percent": 3.916}, {"sql": "select * from `parcours` where `parcours`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 124}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.0009699999999999999, "duration_str": "970μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:124", "connection": "imsaaapp", "start_percent": 59.965, "width_percent": 4.221}, {"sql": "select * from `semestres` where `semestres`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 125}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00075, "duration_str": "750μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:125", "connection": "imsaaapp", "start_percent": 64.186, "width_percent": 3.264}, {"sql": "select * from `annee_universitaires` where `annee_universitaires`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 126}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00488, "duration_str": "4.88ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:126", "connection": "imsaaapp", "start_percent": 67.45, "width_percent": 21.236}, {"sql": "select * from `niveaux` where `niveaux`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 127}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:127", "connection": "imsaaapp", "start_percent": 88.686, "width_percent": 3.394}, {"sql": "select * from `users` where exists (select * from `roles` inner join `role_user` on `roles`.`id` = `role_user`.`role_id` where `users`.`id` = `role_user`.`user_id` and `role_id` = 2) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Livewire\\Ues.php", "line": 128}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 41}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 93}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 35}], "duration": 0.00182, "duration_str": "1.82ms", "stmt_id": "\\app\\Http\\Livewire\\Ues.php:128", "connection": "imsaaapp", "start_percent": 92.08, "width_percent": 7.92}]}, "models": {"data": {"App\\Models\\Matiere": 2, "App\\Models\\AnneeUniversitaire": 8, "App\\Models\\Semestre": 11, "App\\Models\\Niveau": 6, "App\\Models\\Parcour": 25, "App\\Models\\Ue": 1, "App\\Models\\User": 53}, "count": 106}, "livewire": {"data": {"ues #yvXOZF9dXaAK6dkWGnNp": "array:5 [\n  \"data\" => array:35 [\n    \"currentPage\" => \"liste\"\n    \"query\" => \"\"\n    \"selectedParcours\" => []\n    \"selectedNiveaux\" => []\n    \"filtreAnnee\" => \"7\"\n    \"newUe\" => []\n    \"editUe\" => []\n    \"editingUeId\" => null\n    \"expandedUes\" => []\n    \"showQuickAddModal\" => false\n    \"showEcModal\" => false\n    \"ecModalMode\" => \"add\"\n    \"ecForm\" => []\n    \"currentEcUe\" => null\n    \"currentEcId\" => null\n    \"showDuplicationModal\" => false\n    \"duplicationStep\" => \"selection\"\n    \"selectedUesForDuplication\" => []\n    \"targetParcours\" => []\n    \"targetAnneeId\" => \"\"\n    \"duplicationPreview\" => []\n    \"editablePreviewData\" => []\n    \"duplicationResults\" => []\n    \"duplicationFilterAnnee\" => \"\"\n    \"duplicationFilterParcours\" => []\n    \"duplicationFilterSemestres\" => []\n    \"duplicationQuery\" => \"\"\n    \"selectAllUes\" => false\n    \"enseignantQuery\" => \"\"\n    \"filteredEnseignants\" => []\n    \"showAddEnseignantForm\" => false\n    \"newEnseignant\" => array:5 [\n      \"nom\" => \"\"\n      \"prenom\" => \"\"\n      \"email\" => \"\"\n      \"telephone1\" => \"\"\n      \"sexe\" => \"M\"\n    ]\n    \"recentlyAssignedEnseignants\" => []\n    \"page\" => 1\n    \"paginators\" => array:1 [\n      \"page\" => 1\n    ]\n  ]\n  \"name\" => \"ues\"\n  \"view\" => \"livewire.deraq.ue.index\"\n  \"component\" => \"App\\Http\\Livewire\\Ues\"\n  \"id\" => \"yvXOZF9dXaAK6dkWGnNp\"\n]"}, "count": 1}, "gate": {"count": 0, "messages": []}, "session": {"_token": "VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9", "url": "[]", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000/pedagogiques/ue\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1758092143\n]"}, "request": {"path_info": "/livewire/message/ues", "status_code": "<pre class=sf-dump id=sf-dump-1083446807 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1083446807\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-23981267 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-23981267\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1635525715 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">yvXOZF9dXaAK6dkWGnNp</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ues</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">fr</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"15 characters\">pedagogiques/ue</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">4e405531</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:35</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>currentPage</span>\" => \"<span class=sf-dump-str title=\"5 characters\">liste</span>\"\n      \"<span class=sf-dump-key>query</span>\" => \"\"\n      \"<span class=sf-dump-key>selectedParcours</span>\" => []\n      \"<span class=sf-dump-key>selectedNiveaux</span>\" => []\n      \"<span class=sf-dump-key>filtreAnnee</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>newUe</span>\" => []\n      \"<span class=sf-dump-key>editUe</span>\" => []\n      \"<span class=sf-dump-key>editingUeId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>expandedUes</span>\" => []\n      \"<span class=sf-dump-key>showQuickAddModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>showEcModal</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>ecModalMode</span>\" => \"<span class=sf-dump-str title=\"3 characters\">add</span>\"\n      \"<span class=sf-dump-key>ecForm</span>\" => []\n      \"<span class=sf-dump-key>currentEcUe</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>currentEcId</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>showDuplicationModal</span>\" => <span class=sf-dump-const>true</span>\n      \"<span class=sf-dump-key>duplicationStep</span>\" => \"<span class=sf-dump-str title=\"10 characters\">processing</span>\"\n      \"<span class=sf-dump-key>selectedUesForDuplication</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-num>80</span>\n      </samp>]\n      \"<span class=sf-dump-key>targetParcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>targetAnneeId</span>\" => \"<span class=sf-dump-str>7</span>\"\n      \"<span class=sf-dump-key>duplicationPreview</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"9 characters\">COMPTAGEN</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"4 characters\">FISC</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>target_parcours</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"35 characters\">Tourisme H&#244;tellerie et Restauration</span>\"\n              \"<span class=sf-dump-key>sigle</span>\" => \"<span class=sf-dump-str title=\"3 characters\">THR</span>\"\n            </samp>]\n          </samp>]\n          \"<span class=sf-dump-key>conflicts</span>\" => []\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>editablePreviewData</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue</span>\" => <span class=sf-dump-note>array:8</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>80</span>\n            \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n            \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"15 characters\">IMTBL11EJFQSFDQ</span>\"\n            \"<span class=sf-dump-key>credit</span>\" => <span class=sf-dump-num>7</span>\n            \"<span class=sf-dump-key>niveau_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>semestre_id</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>niveau_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">1&#232;re ann&#233;e</span>\"\n            \"<span class=sf-dump-key>semestre_nom</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Semestre 1</span>\"\n          </samp>]\n          \"<span class=sf-dump-key>ecs</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"21 characters\">Comptabilit&#233; G&#233;n&#233;rale</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"12 characters\">COMPTAGENQSD</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n            <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=7 class=sf-dump-compact>\n              \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n              \"<span class=sf-dump-key>nom</span>\" => \"<span class=sf-dump-str title=\"9 characters\">Fiscalit&#233;</span>\"\n              \"<span class=sf-dump-key>code</span>\" => \"<span class=sf-dump-str title=\"7 characters\">FISCSDF</span>\"\n              \"<span class=sf-dump-key>syllabus</span>\" => <span class=sf-dump-const>null</span>\n              \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-num>149</span>\n              \"<span class=sf-dump-key>enseignant_nom</span>\" => \"<span class=sf-dump-str title=\"8 characters\"> PARFAIT</span>\"\n            </samp>]\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>duplicationResults</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-key>80</span> => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>ue_code</span>\" => \"<span class=sf-dump-str title=\"10 characters\">IMTBL11EJF</span>\"\n          \"<span class=sf-dump-key>ue_nom</span>\" => \"<span class=sf-dump-str title=\"47 characters\">ENVIRONNEMENT ECONOMIQUE JURIDIQUE ET FISCALIT&#201;</span>\"\n          \"<span class=sf-dump-key>created_count</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>target_count</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>created_ue_ids</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=sf-dump-num>474</span>\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>duplicationFilterAnnee</span>\" => \"\"\n      \"<span class=sf-dump-key>duplicationFilterParcours</span>\" => []\n      \"<span class=sf-dump-key>duplicationFilterSemestres</span>\" => []\n      \"<span class=sf-dump-key>duplicationQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>selectAllUes</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>enseignantQuery</span>\" => \"\"\n      \"<span class=sf-dump-key>filteredEnseignants</span>\" => []\n      \"<span class=sf-dump-key>showAddEnseignantForm</span>\" => <span class=sf-dump-const>false</span>\n      \"<span class=sf-dump-key>newEnseignant</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>nom</span>\" => \"\"\n        \"<span class=sf-dump-key>prenom</span>\" => \"\"\n        \"<span class=sf-dump-key>email</span>\" => \"\"\n        \"<span class=sf-dump-key>telephone1</span>\" => \"\"\n        \"<span class=sf-dump-key>sexe</span>\" => \"<span class=sf-dump-str>M</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>recentlyAssignedEnseignants</span>\" => []\n      \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      \"<span class=sf-dump-key>paginators</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>page</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">3a4da68878d39b3c32dc1573bf32c72c9ef001359cda41d8723114e331cd3791</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">bofr</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"21 characters\">closeDuplicationModal</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1635525715\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-866100311 data-indent-pad=\"  \"><span class=sf-dump-note>array:20</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">2324</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVPRWhHR3JBN0kwNEpFaXBabVAzdHc9PSIsInZhbHVlIjoiVXI1a0U2UUxqQXZqT2ZncjlTVTM5VnJ1S0pCTFU1OUgvbk9COEVzTDFQMklMNCtFYUtZWGRHcVI0K1hGbmNJUmpLNjZiR2lsSWJWVzFkT2JsZ0VLaTFmSGthdGwzaGxyM1Yybmk4Vm5XSWxjRWJvamFpYndjOXhIMWp1bXBvVXgiLCJtYWMiOiJmNDM1MTdjNzU3NWZlODNkZDkyYWMyNGJmNzVmYzNhMGJmYTUzZjk4NmZkODFkYjMzNzQ5NTdkNjM3YmU4Y2IyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjViVmhIOEJQdnp3Tmpnc2lCMWJjb1E9PSIsInZhbHVlIjoiU3h0eUNwWGRnbkFkbWl3ODk2NmJTU2x2QklxNHdHVzV4STA2M1VISFUrZ1NoMUYwU2FGS1lKV29GMzVCZzQ0ZTJRaXRMWjQwUDRoclJzUzlVU1FDMitKOGNmR24yaU1waG5WclR3SnU1dVpIOFFvR1BVZHZVUHJQamthVGZaY3kiLCJtYWMiOiI4MDljZDA2NzY0NjEwYmEyMmFlZTZiNDk3Njk5Y2RlMGQ4MmUyNWMzZGM5YTM0MmQzNThiOGY0YzlkMjJkOWNlIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-866100311\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1841967695 data-indent-pad=\"  \"><span class=sf-dump-note>array:37</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"35 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61019</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"45 characters\">C:\\xampp\\htdocs\\ImsaaProject\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"21 characters\">/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"31 characters\">/index.php/livewire/message/ues</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2324</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"4 characters\">2324</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"66 characters\">&quot;Chromium&quot;;v=&quot;140&quot;, &quot;Not=A?Brand&quot;;v=&quot;24&quot;, &quot;Microsoft Edge&quot;;v=&quot;140&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">fr,fr-FR;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1263 characters\">remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d=eyJpdiI6InpOaFc4K29BZzU3TFZ4MXBCcytLYmc9PSIsInZhbHVlIjoiSUFid3o2WFdxZVFVTW5kTHAybWV5LzhUQTVLdHdHZlZMejFWaGM3RmNlMDVYMlYvU0NZWGhQR1R0T1FBUkpQZnpZTzVSMGNPQ1JOMFFPSzhDTHVmSU9RVE1sV0FNbXQ5R0xIcnU0eTdFa1dKb2pmbVRoazhGeEtUcGxHVEVHbFpBVERHOEltYXlDYldDb3Q1cnE5UTFudm9Oell5VGx4Uk1xTkRoVlJzaHdFOHpUM3dVMmhnU1E2OUtqTFpZTUdaRlpKZ3BEYk1pWXJFZHZTOHUyMVVMUTRpNHRQaXNHdHNVR0RJY3ZHTEhDMD0iLCJtYWMiOiIxZDk5MGRjOWJhNGRlZWVjZWFiMDE5MzVhNzk5ZmZhYTkwMTNmZjQzZDhlYTk0MjAyMWE2MTJjMjMwNGVlODg5IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlVPRWhHR3JBN0kwNEpFaXBabVAzdHc9PSIsInZhbHVlIjoiVXI1a0U2UUxqQXZqT2ZncjlTVTM5VnJ1S0pCTFU1OUgvbk9COEVzTDFQMklMNCtFYUtZWGRHcVI0K1hGbmNJUmpLNjZiR2lsSWJWVzFkT2JsZ0VLaTFmSGthdGwzaGxyM1Yybmk4Vm5XSWxjRWJvamFpYndjOXhIMWp1bXBvVXgiLCJtYWMiOiJmNDM1MTdjNzU3NWZlODNkZDkyYWMyNGJmNzVmYzNhMGJmYTUzZjk4NmZkODFkYjMzNzQ5NTdkNjM3YmU4Y2IyIiwidGFnIjoiIn0%3D; scolarite_imsaa_session=eyJpdiI6IjViVmhIOEJQdnp3Tmpnc2lCMWJjb1E9PSIsInZhbHVlIjoiU3h0eUNwWGRnbkFkbWl3ODk2NmJTU2x2QklxNHdHVzV4STA2M1VISFUrZ1NoMUYwU2FGS1lKV29GMzVCZzQ0ZTJRaXRMWjQwUDRoclJzUzlVU1FDMitKOGNmR24yaU1waG5WclR3SnU1dVpIOFFvR1BVZHZVUHJQamthVGZaY3kiLCJtYWMiOiI4MDljZDA2NzY0NjEwYmEyMmFlZTZiNDk3Njk5Y2RlMGQ4MmUyNWMzZGM5YTM0MmQzNThiOGY0YzlkMjJkOWNlIiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1758098277.5684</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1758098277</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841967695\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1255498280 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>remember_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>scolarite_imsaa_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">5YQflWm2dljwYG047t9akNiy9aKBn7vbP7mVMnSN</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1255498280\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1294228519 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Wed, 17 Sep 2025 08:37:58 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJoRC9XOEJZdzA3YUVpSThMZ3hmMXc9PSIsInZhbHVlIjoiQkZMV0xZZ2tTcnc4Y1NkKzhXR2xNVmEvanVud0tWUUUrNW5hcTVINU93YmdQR3lNNXFEUVBhUXB2QUV2eHJGcjNFSlAxK1dtUzNsY3pGL3F3OUNxZWpEQTJXajZMS2tueDF3bjFLWXkrT2tjK3JoUE5UaXNKNWsxZG5uUGR2c3QiLCJtYWMiOiJiZDQzMWI2YTYyYmI5YzQ4MzQxM2U4ZGYxNGU1Y2JkZGQ3NTk4OTAyM2M5NjU2MDI4NGM3MzI1N2VlMThkZDc3IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:37:58 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">scolarite_imsaa_session=eyJpdiI6ImZuZWdJVURFa0RTaEZ3RzcycGdVMnc9PSIsInZhbHVlIjoiOEFIb2JwbjZxQ0Q5NXlRZkM0ZmxsVldxT3U1clIyWllKcHNCM2dXUWxXaks4azRabDh4dVI0d3ViMTFPd3lxVkF1cXJYVG90RXNwL1BHSVZCcUVLQ1E0eXNQZmYwSE5uYm9hbFV1TVh0a2hyY0VDWnNuVzExeHlNdHRPY3gyNXYiLCJtYWMiOiI1MzllMmFmODUyNWE5ZTY2ZTI1MmQ1ODM0MGJmYTMyMmQ4YzdlNmE5NzdlMjQyYjE4NjRlN2UyYzM0YjI4OTc0IiwidGFnIjoiIn0%3D; expires=Wed, 17 Sep 2025 10:37:58 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJoRC9XOEJZdzA3YUVpSThMZ3hmMXc9PSIsInZhbHVlIjoiQkZMV0xZZ2tTcnc4Y1NkKzhXR2xNVmEvanVud0tWUUUrNW5hcTVINU93YmdQR3lNNXFEUVBhUXB2QUV2eHJGcjNFSlAxK1dtUzNsY3pGL3F3OUNxZWpEQTJXajZMS2tueDF3bjFLWXkrT2tjK3JoUE5UaXNKNWsxZG5uUGR2c3QiLCJtYWMiOiJiZDQzMWI2YTYyYmI5YzQ4MzQxM2U4ZGYxNGU1Y2JkZGQ3NTk4OTAyM2M5NjU2MDI4NGM3MzI1N2VlMThkZDc3IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:37:58 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"423 characters\">scolarite_imsaa_session=eyJpdiI6ImZuZWdJVURFa0RTaEZ3RzcycGdVMnc9PSIsInZhbHVlIjoiOEFIb2JwbjZxQ0Q5NXlRZkM0ZmxsVldxT3U1clIyWllKcHNCM2dXUWxXaks4azRabDh4dVI0d3ViMTFPd3lxVkF1cXJYVG90RXNwL1BHSVZCcUVLQ1E0eXNQZmYwSE5uYm9hbFV1TVh0a2hyY0VDWnNuVzExeHlNdHRPY3gyNXYiLCJtYWMiOiI1MzllMmFmODUyNWE5ZTY2ZTI1MmQ1ODM0MGJmYTMyMmQ4YzdlNmE5NzdlMjQyYjE4NjRlN2UyYzM0YjI4OTc0IiwidGFnIjoiIn0%3D; expires=Wed, 17-Sep-2025 10:37:58 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1294228519\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1515052297 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">VKYATa7oShVDJ3y5RnYDfwPiOsU0QW5FOfE6OcD9</span>\"\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">http://127.0.0.1:8000/pedagogiques/ue</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1758092143</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1515052297\", {\"maxDepth\":0})</script>\n"}}