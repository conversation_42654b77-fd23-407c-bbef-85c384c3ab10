<?php

namespace App\Http\Livewire;

use App\Models\AnneeUniversitaire;
use App\Models\Matiere;
use App\Models\Niveau;
use App\Models\Parcour;
use App\Models\Semestre;
use App\Models\Ue;
use App\Models\User;
use Livewire\Component;
use Livewire\WithPagination;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class Ues extends Component
{
    use WithPagination;
    protected $paginationTheme = "bootstrap";
    public $currentPage = PAGELIST;

    protected $listeners = [
        'duplicationFilterChanged' => 'updateSelectAllState'
    ];

    
    // Recherche et filtres
    public $query = '';
    public $selectedParcours = [];
    public $selectedNiveaux = [];
    public $filtreAnnee = '';
    
    // Gestion UE
    public $newUe = [];
    public $editUe = [];
    public $editingUeId = null;
    public $expandedUes = [];
    public $showQuickAddModal = false;
    
    // Gestion EC
    public $showEcModal = false;
    public $ecModalMode = 'add';
    public $ecForm = [];
    public $currentEcUe = null;
    public $currentEcId = null;

    // Gestion Duplication
    public $showDuplicationModal = false;
    public $duplicationStep = 'selection'; // 'selection', 'preview', 'processing'
    public $selectedUesForDuplication = [];
    public $targetParcours = [];
    public $targetAnneeId = '';
    public $duplicationPreview = [];
    public $editablePreviewData = [];
    public $duplicationResults = [];
    public $validationStatus = 'pending'; // 'pending', 'validating', 'valid', 'invalid'
    public $lastValidationMessage = '';

    // Filtres pour la sélection des UEs à dupliquer
    public $duplicationFilterAnnee = '';
    public $duplicationFilterParcours = [];
    public $duplicationFilterSemestres = [];
    public $duplicationQuery = '';
    public $selectAllUes = false;
    
    // Gestion recherche enseignant dans modal EC
    public $enseignantQuery = '';
    public $filteredEnseignants = [];
    public $showAddEnseignantForm = false;
    public $newEnseignant = [
        'nom' => '',
        'prenom' => '',
        'email' => '',
        'telephone1' => '',
        'sexe' => 'M'
    ];
    public $recentlyAssignedEnseignants = [];

    // Reset pagination sur changement des filtres
    public function updatingQuery() { $this->resetPage(); }
    public function updatingSelectedParcours() { $this->resetPage(); }
    public function updatingSelectedNiveaux() { $this->resetPage(); }
    public function updatingFiltreAnnee() { $this->resetPage(); }

    public function render()
    {
        // Construire la requête avec filtres
        $query = Ue::query()->with(['parcours', 'niveau', 'semestre', 'annee', 'matiere.user']);

        // Recherche globale
        if(!empty($this->query)) {
            $query->where(function($q) {
                $q->where('nom', 'like', '%'. $this->query .'%')
                  ->orWhere('code', 'like', '%'. $this->query .'%')
                  ->orWhereHas('parcours', function($q) {
                      $q->where('sigle', 'like', '%'. $this->query .'%');
                  });
            });
        }

        // Appliquer les filtres sélectionnés
        if(count($this->selectedParcours) > 0) {
            $query->whereIn('parcour_id', $this->selectedParcours);
        }

        if(count($this->selectedNiveaux) > 0) {
            $query->whereIn('niveau_id', $this->selectedNiveaux);
        }

        if(!empty($this->filtreAnnee)) {
            $query->where('annee_universitaire_id', $this->filtreAnnee);
        }

        // Nombre de filtres actifs pour l'UI
        $activeFiltersCount = count($this->selectedParcours) + count($this->selectedNiveaux) + ($this->filtreAnnee ? 1 : 0);

        // Update select all state when filters change (only if duplication modal is open)
        if ($this->showDuplicationModal) {
            $this->updateSelectAllState();
        }

        return view('livewire.deraq.ue.index', [
            "ues" => $query->paginate(10),
            "parcours" => Parcour::all(),
            "semestres" => Semestre::all(),
            "annees" => AnneeUniversitaire::all(),
            "niveaux" => Niveau::all(),
            "enseignants" => User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))->get(),
            "activeFiltersCount" => $activeFiltersCount
        ])
        ->extends('layouts.backend')
        ->section('content');
    }

    // Validation
    protected function rules()
    {
        if($this->ecModalMode === 'edit') {
            return [
                'ecForm.nom' => 'required',
                'ecForm.code' => 'required',
                'ecForm.user_id' => 'nullable',
            ];
        } elseif($this->ecModalMode === 'add') {
            return [
                'ecForm.nom' => 'required',
                'ecForm.code' => 'required|unique:matieres,code',
                'ecForm.user_id' => 'nullable',
            ];
        } elseif($this->editingUeId) {
            return [
                'editUe.nom' => 'required',
                'editUe.code' => 'required',
                'editUe.credit' => 'required|numeric',
                'editUe.parcour_id' => 'required',
                'editUe.semestre_id' => 'required',
                'editUe.niveau_id' => 'required',
                'editUe.annee_universitaire_id' => 'required',
            ];
        } else {
            // Rules for adding a new UE
            return [
                'newUe.nom' => 'required',
                'newUe.code' => 'required', // Retiré unique temporairement
                'newUe.credit' => 'required|numeric',
                'newUe.parcour_id' => 'required',
                'newUe.niveau_id' => 'required',
                'newUe.semestre_id' => 'required',
                'newUe.annee_universitaire_id' => 'required',
            ];
        }
    }

    // Gestion des UE
    public function addUe()
    {
        $validateArr = [
            'newUe.nom' => 'required|string|max:255',
            'newUe.code' => 'required|string|max:50|unique:ues,code', // Ensure code is unique in the 'ues' table
            'newUe.credit' => 'required|numeric|min:0',
            'newUe.parcour_id' => 'required|exists:parcours,id', // Check if parcour_id exists in 'parcours' table
            'newUe.niveau_id' => 'required|exists:niveaux,id', // Check if niveau_id exists in 'niveaux' table
            'newUe.semestre_id' => 'required|exists:semestres,id', // Check if semestre_id exists in 'semestres' table
            'newUe.annee_universitaire_id' => 'required|exists:annee_universitaires,id', // Check if annee_universitaire_id exists in 'annee_universitaires' table
        ];
        
        $this->validate($validateArr);

        
        Ue::create($this->newUe);
        
        $this->newUe = [];
        $this->showQuickAddModal = false;
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE créée avec succès!"
        ]);
    }
    
    public function startEditing($ueId)
    {
        $ue = Ue::findOrFail($ueId);
        $this->editUe = $ue->toArray();
        $this->editingUeId = $ueId;
    }
    
    public function cancelEdit()
    {
        $this->editUe = [];
        $this->editingUeId = null;
    }
    
    public function updateUe()
    {
        $validateArr = [
            'editUe.nom' => 'required|string|max:255',
            // Ensure code is unique, ignoring the current UE being edited
            'editUe.code' => 'required|string|max:50|unique:ues,code,' . $this->editingUeId, 
            'editUe.credit' => 'required|numeric|min:0',
            'editUe.parcour_id' => 'required|exists:parcours,id', // Check if parcour_id exists in 'parcours' table
            'editUe.niveau_id' => 'required|exists:niveaux,id', // Check if niveau_id exists in 'niveaux' table
            'editUe.semestre_id' => 'required|exists:semestres,id', // Check if semestre_id exists in 'semestres' table
            'editUe.annee_universitaire_id' => 'required|exists:annee_universitaires,id', // Check if annee_universitaire_id exists in 'annee_universitaires' table
        ];
        
        $this->validate($validateArr);
        
        Ue::find($this->editingUeId)->update($this->editUe);
        
        $this->editUe = [];
        $this->editingUeId = null;
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE mise à jour avec succès!"
        ]);
    }
    
    public function deleteUe($id)
    {
        // Vérifier si l'UE a des ECs associés
        $ue = Ue::with('matiere')->find($id);
        
        if($ue->matiere->count() > 0) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Impossible de supprimer cette UE car elle contient des ECs. Supprimez d'abord les ECs."
            ]);
            return;
        }
        
        Ue::destroy($id);
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "UE supprimée avec succès!"
        ]);
    }
    
    // Gestion de l'affichage des ECs
    public function toggleEcList($ueId)
    {
        if(in_array($ueId, $this->expandedUes)) {
            $this->expandedUes = array_diff($this->expandedUes, [$ueId]);
        } else {
            $this->expandedUes[] = $ueId;
        }
    }
    
    // Gestion des recherches enseignants dans le EC modal
    public function updatedEnseignantQuery()
    {
        if (strlen($this->enseignantQuery) >= 2) {
            $this->filteredEnseignants = User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
                ->where(function($query) {
                    $query->where('nom', 'like', '%'. $this->enseignantQuery .'%')
                        ->orWhere('prenom', 'like', '%'. $this->enseignantQuery .'%')
                        ->orWhere('email', 'like', '%'. $this->enseignantQuery .'%');
                })
                ->orderByRaw("CASE WHEN id IN (" . implode(',', array_filter($this->recentlyAssignedEnseignants)) . ") THEN 0 ELSE 1 END")
                ->limit(5)
                ->get();
        } else {
            $this->filteredEnseignants = collect([]);
        }
    }

    // Méthode pour sélectionner un enseignant filtré
    public function selectEnseignant($id)
    {
        $this->ecForm['user_id'] = $id;
        
        // Ajouter l'ID aux enseignants récemment assignés
        if (!in_array($id, $this->recentlyAssignedEnseignants)) {
            $this->recentlyAssignedEnseignants[] = $id;
            // Garder uniquement les 5 derniers
            if (count($this->recentlyAssignedEnseignants) > 5) {
                array_shift($this->recentlyAssignedEnseignants);
            }
        }
        
        $this->enseignantQuery = '';
        $this->filteredEnseignants = collect([]);
    }

    // Méthode pour afficher le formulaire d'ajout d'enseignant
    public function toggleAddEnseignantForm()
    {
        $this->showAddEnseignantForm = !$this->showAddEnseignantForm;
        if (!$this->showAddEnseignantForm) {
            $this->resetNewEnseignant();
        }
    }

    // Méthode pour réinitialiser le formulaire d'ajout d'enseignant
    public function resetNewEnseignant()
    {
        $this->newEnseignant = [
            'nom' => '',
            'prenom' => '',
            'email' => '',
            'telephone1' => '',
            'sexe' => 'M'
        ];
    }

    // Méthode pour créer un nouvel enseignant depuis le modal d'EC
    public function createEnseignant()
    {
        $this->validate([
            'newEnseignant.nom' => 'required',
            'newEnseignant.prenom' => 'required',
            'newEnseignant.telephone1' => 'nullable|numeric|unique:users,telephone1',
            'newEnseignant.sexe' => 'required'
        ]);
        
        // Générer un mot de passe aléatoire
        $password = Str::random(10);
        
        // Créer l'utilisateur
        $user = User::create([
            'nom' => $this->newEnseignant['nom'],
            'prenom' => $this->newEnseignant['prenom'],
            'telephone1' => $this->newEnseignant['telephone1'],
            'sexe' => $this->newEnseignant['sexe'],
            'password' => Hash::make($password),
            'photo' => 'media/avatars/avatar0.jpg'
        ]);
        
        // Attribuer le rôle d'enseignant
        $user->roles()->attach(2);
        
        // Optionnel : Envoyer un email avec les identifiants
        // Mail::to($user->email)->send(new SignUp($user->nom, $user->prenom, $password, $user->email));
        
        // Sélectionner le nouvel enseignant
        $this->ecForm['user_id'] = $user->id;
        $this->recentlyAssignedEnseignants[] = $user->id;
        
        // Fermer le formulaire
        $this->showAddEnseignantForm = false;
        $this->resetNewEnseignant();
        
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "Enseignant créé avec succès!"
        ]);
    }
    
    // Gestion des EC (Éléments Constitutifs)
    public function addQuickEC($ueId)
    {
        $this->currentEcUe = Ue::findOrFail($ueId);
        $this->ecForm = [
            'code' => '',
            'nom' => '',
            'user_id' => '',
            'ue_id' => $ueId
        ];
        $this->ecModalMode = 'add';
        $this->showEcModal = true;
        $this->loadRecentEnseignants();
        
        // S'assurer que l'UE est étendue pour voir l'EC ajouté
        if(!in_array($ueId, $this->expandedUes)) {
            $this->expandedUes[] = $ueId;
        }
    }
    
    // Charger les enseignants récemment assignés
    private function loadRecentEnseignants()
    {
        if (count($this->recentlyAssignedEnseignants) === 0) {
            $recentlyAssigned = Matiere::whereNotNull('user_id')
                ->select('user_id')
                ->distinct()
                ->orderBy('updated_at', 'desc')
                ->limit(5)
                ->pluck('user_id')
                ->toArray();
            
            $this->recentlyAssignedEnseignants = array_filter($recentlyAssigned);
        }
    }
    
    public function editEC($matiereId)
    {
        $matiere = Matiere::findOrFail($matiereId);
        $this->currentEcUe = $matiere->ue;
        $this->currentEcId = $matiereId;
        $this->ecForm = $matiere->toArray();
        $this->ecModalMode = 'edit';
        $this->showEcModal = true;
        $this->loadRecentEnseignants();
    }
    
    public function saveEC()
    {
        $this->validate();
        
        if($this->ecModalMode === 'add') {
            Matiere::create($this->ecForm);
            $message = "EC créé avec succès!";
        } else {
            Matiere::find($this->currentEcId)->update($this->ecForm);
            $message = "EC mis à jour avec succès!";
        }
        
        $this->closeEcModal();
        $this->dispatchBrowserEvent("showSuccessMessage", ["message" => $message]);
    }
    
    public function closeEcModal()
    {
        $this->showEcModal = false;
        $this->ecForm = [];
        $this->currentEcUe = null;
        $this->currentEcId = null;
        $this->enseignantQuery = '';
        $this->filteredEnseignants = collect([]);
        $this->showAddEnseignantForm = false;
    }
    
    public function deleteEC($id)
    {
        Matiere::destroy($id);
        $this->dispatchBrowserEvent("showSuccessMessage", [
            "message" => "EC supprimé avec succès!"
        ]);
    }

    // ==================== DUPLICATION METHODS ====================

    /**
     * Open the duplication modal and initialize selection step
     */
    public function openDuplicationModal()
    {
        $this->resetDuplicationData();
        $this->duplicationStep = 'selection';
        $this->showDuplicationModal = true;
    }

    /**
     * Close the duplication modal and reset all data
     */
    public function closeDuplicationModal()
    {
        $this->showDuplicationModal = false;
        $this->resetDuplicationData();
    }

    /**
     * Reset all duplication-related data
     */
    private function resetDuplicationData()
    {
        $this->selectedUesForDuplication = [];
        $this->targetParcours = [];
        $this->targetAnneeId = '';
        $this->duplicationPreview = [];
        $this->editablePreviewData = [];
        $this->duplicationResults = [];
        $this->duplicationStep = 'selection';
        $this->duplicationFilterAnnee = '';
        $this->duplicationFilterParcours = [];
        $this->duplicationFilterSemestres = [];
        $this->duplicationQuery = '';
        $this->selectAllUes = false;
        $this->validationStatus = 'pending';
        $this->lastValidationMessage = '';
    }

    /**
     * Toggle UE selection for duplication
     */
    public function toggleUeSelection($ueId)
    {
        if (in_array($ueId, $this->selectedUesForDuplication)) {
            $this->selectedUesForDuplication = array_diff($this->selectedUesForDuplication, [$ueId]);
        } else {
            $this->selectedUesForDuplication[] = $ueId;
        }

        // Update selectAllUes state based on current selection
        $this->updateSelectAllState();
    }

    /**
     * Toggle select all UEs
     */
    public function toggleSelectAllUes()
    {
        if ($this->selectAllUes) {
            // Select all filtered UEs
            $filteredUes = $this->getFilteredUesForDuplication();
            $this->selectedUesForDuplication = array_unique(array_merge(
                $this->selectedUesForDuplication,
                $filteredUes->pluck('id')->toArray()
            ));
        } else {
            // Deselect all filtered UEs
            $filteredUes = $this->getFilteredUesForDuplication();
            $filteredIds = $filteredUes->pluck('id')->toArray();
            $this->selectedUesForDuplication = array_diff($this->selectedUesForDuplication, $filteredIds);
        }
    }

    /**
     * Update the select all state based on current selection
     */
    private function updateSelectAllState()
    {
        $filteredUes = $this->getFilteredUesForDuplication();
        $filteredIds = $filteredUes->pluck('id')->toArray();

        if (empty($filteredIds)) {
            $this->selectAllUes = false;
            return;
        }

        // Check if all filtered UEs are selected
        $selectedFilteredIds = array_intersect($this->selectedUesForDuplication, $filteredIds);
        $this->selectAllUes = count($selectedFilteredIds) === count($filteredIds);
    }

    /**
     * Get filtered UEs for duplication selection
     */
    public function getFilteredUesForDuplication()
    {
        $query = Ue::query()->with(['parcours', 'niveau', 'semestre', 'annee', 'matiere']);

        // Apply search filter
        if (!empty($this->duplicationQuery)) {
            $query->where(function($q) {
                $q->where('nom', 'like', '%'. $this->duplicationQuery .'%')
                  ->orWhere('code', 'like', '%'. $this->duplicationQuery .'%')
                  ->orWhereHas('parcours', function($q) {
                      $q->where('sigle', 'like', '%'. $this->duplicationQuery .'%');
                  });
            });
        }

        // Apply parcours filter
        if (count($this->duplicationFilterParcours) > 0) {
            $query->whereIn('parcour_id', $this->duplicationFilterParcours);
        }

        // Apply semestre filter
        if (count($this->duplicationFilterSemestres) > 0) {
            $query->whereIn('semestre_id', $this->duplicationFilterSemestres);
        }

        // Apply academic year filter
        if (!empty($this->duplicationFilterAnnee)) {
            $query->where('annee_universitaire_id', $this->duplicationFilterAnnee);
        }

        return $query->get();
    }

    /**
     * Clear duplication filters
     */
    public function clearDuplicationFilters()
    {
        $this->duplicationFilterAnnee = '';
        $this->duplicationFilterParcours = [];
        $this->duplicationFilterSemestres = [];
        $this->duplicationQuery = '';
        $this->updateSelectAllState();
    }

    /**
     * Watchers for duplication filters to update select all state
     */
    public function updatedDuplicationFilterAnnee()
    {
        $this->updateSelectAllState();
    }

    public function updatedDuplicationFilterParcours()
    {
        $this->updateSelectAllState();
    }

    public function updatedDuplicationFilterSemestres()
    {
        $this->updateSelectAllState();
    }

    public function updatedDuplicationQuery()
    {
        $this->updateSelectAllState();
    }

    /**
     * Reset validation status when preview data is updated
     */
    public function updatedEditablePreviewData()
    {
        $this->validationStatus = 'pending';
        $this->lastValidationMessage = '';
    }

    /**
     * Proceed to preview step - generate preview data
     */
    public function proceedToPreview()
    {
        // Validate selection
        $validationResult = $this->validateDuplicationSelection();
        if (!$validationResult['valid']) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => $validationResult['message']
            ]);
            return;
        }

        // Show warnings if any conflicts detected
        if (!empty($validationResult['warnings'])) {
            $this->dispatchBrowserEvent("showWarningMessage", [
                "message" => $validationResult['warning_summary']
            ]);
        }

        // Generate preview data
        $this->generateDuplicationPreview();
        $this->duplicationStep = 'preview';
    }

    /**
     * Validate duplication selection data
     */
    private function validateDuplicationSelection()
    {
        // Check if UEs are selected
        if (empty($this->selectedUesForDuplication)) {
            return [
                'valid' => false,
                'message' => "Veuillez sélectionner au moins une UE à dupliquer."
            ];
        }

        // Check if target parcours are selected
        if (empty($this->targetParcours)) {
            return [
                'valid' => false,
                'message' => "Veuillez sélectionner au moins un parcours de destination."
            ];
        }

        // Check if target academic year is selected
        if (empty($this->targetAnneeId)) {
            return [
                'valid' => false,
                'message' => "Veuillez sélectionner une année universitaire de destination."
            ];
        }

        // Validate that selected UEs exist
        $existingUeIds = Ue::whereIn('id', $this->selectedUesForDuplication)->pluck('id')->toArray();
        $missingUeIds = array_diff($this->selectedUesForDuplication, $existingUeIds);
        if (!empty($missingUeIds)) {
            return [
                'valid' => false,
                'message' => "Certaines UE sélectionnées n'existent plus. Veuillez actualiser la page."
            ];
        }

        // Validate that target parcours exist
        $existingParcourIds = Parcour::whereIn('id', $this->targetParcours)->pluck('id')->toArray();
        $missingParcourIds = array_diff($this->targetParcours, $existingParcourIds);
        if (!empty($missingParcourIds)) {
            return [
                'valid' => false,
                'message' => "Certains parcours de destination n'existent plus. Veuillez actualiser la page."
            ];
        }

        // Validate that target academic year exists
        if (!AnneeUniversitaire::where('id', $this->targetAnneeId)->exists()) {
            return [
                'valid' => false,
                'message' => "L'année universitaire sélectionnée n'existe plus. Veuillez actualiser la page."
            ];
        }

        // Check for potential conflicts (UEs that already exist)
        $conflicts = [];
        foreach ($this->selectedUesForDuplication as $ueId) {
            $ue = Ue::find($ueId);
            if ($ue) {
                foreach ($this->targetParcours as $parcourId) {
                    $existingUe = Ue::where('code', $ue->code)
                        ->where('parcour_id', $parcourId)
                        ->where('annee_universitaire_id', $this->targetAnneeId)
                        ->first();

                    if ($existingUe) {
                        $parcour = Parcour::find($parcourId);
                        $conflicts[] = "UE '{$ue->code}' existe déjà pour le parcours {$parcour->sigle}";
                    }
                }
            }
        }

        // If there are conflicts, return them as warnings but still allow to proceed
        if (!empty($conflicts)) {
            return [
                'valid' => true,
                'message' => '',
                'warnings' => $conflicts,
                'warning_summary' => "Attention : " . count($conflicts) . " conflit(s) détecté(s). Les UE existantes ne seront pas remplacées."
            ];
        }

        return ['valid' => true, 'message' => '', 'warnings' => []];
    }

    /**
     * Generate preview data for selected UEs
     */
    private function generateDuplicationPreview()
    {
        $this->duplicationPreview = [];
        $this->editablePreviewData = [];

        foreach ($this->selectedUesForDuplication as $ueId) {
            $ue = Ue::with(['matiere.user', 'niveau', 'semestre'])->find($ueId);
            if ($ue) {
                $preview = $ue->getDuplicationPreview($this->targetParcours, $this->targetAnneeId);
                $this->duplicationPreview[$ueId] = $preview;

                // Initialize editable data with current values
                $this->editablePreviewData[$ueId] = [
                    'ue' => $preview['ue'],
                    'ecs' => $preview['ecs']
                ];
            }
        }
    }

    /**
     * Go back to selection step
     */
    public function backToSelection()
    {
        $this->duplicationStep = 'selection';
    }

    /**
     * Validate preview data manually (called by user)
     */
    public function validatePreviewData()
    {
        $this->validationStatus = 'validating';
        $this->lastValidationMessage = '';

        $validationResult = $this->validateDuplicationData();

        if ($validationResult['valid']) {
            $this->validationStatus = 'valid';
            $this->lastValidationMessage = "Toutes les données sont valides ! Vous pouvez procéder à la duplication.";
            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => $this->lastValidationMessage
            ]);
        } else {
            $this->validationStatus = 'invalid';
            $this->lastValidationMessage = $validationResult['message'];
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => $validationResult['message']
            ]);
        }
    }

    /**
     * Execute the duplication process
     */
    public function executeDuplication()
    {
        // Validate data before execution
        $validationResult = $this->validateDuplicationData();
        if (!$validationResult['valid']) {
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => $validationResult['message']
            ]);
            return;
        }

        $this->duplicationStep = 'processing';
        $this->duplicationResults = [];

        try {
            DB::beginTransaction();

            $totalCreated = 0;
            $errors = [];

            foreach ($this->selectedUesForDuplication as $ueId) {
                $ue = Ue::find($ueId);
                if (!$ue) {
                    $errors[] = "UE avec ID {$ueId} introuvable.";
                    continue;
                }

                // Get editable data for this UE
                $ueData = $this->editablePreviewData[$ueId]['ue'] ?? [];
                $ecData = [];

                // Prepare EC data modifications
                foreach ($this->editablePreviewData[$ueId]['ecs'] ?? [] as $ec) {
                    $ecData[$ec['id']] = $ec;
                }

                // Execute duplication
                $createdUeIds = $ue->duplicateToMultipleParcours(
                    $this->targetParcours,
                    $this->targetAnneeId,
                    $ueData,
                    $ecData
                );

                $this->duplicationResults[$ueId] = [
                    'ue_code' => $ue->code,
                    'ue_nom' => $ue->nom,
                    'created_count' => count($createdUeIds),
                    'target_count' => count($this->targetParcours),
                    'created_ue_ids' => $createdUeIds
                ];

                $totalCreated += count($createdUeIds);
            }

            DB::commit();

            // Show success message
            $selectedCount = count($this->selectedUesForDuplication);
            $targetCount = count($this->targetParcours);

            $this->dispatchBrowserEvent("showSuccessMessage", [
                "message" => "Duplication réussie ! {$totalCreated} UE(s) créée(s) à partir de {$selectedCount} UE(s) source(s) vers {$targetCount} parcours."
            ]);

            // Close modal after a delay
            $this->dispatchBrowserEvent('closeDuplicationModalAfterDelay');

        } catch (\Exception $e) {
            DB::rollBack();
            $this->dispatchBrowserEvent("showErrorMessage", [
                "message" => "Erreur lors de la duplication : " . $e->getMessage()
            ]);
        }
    }

    /**
     * Validate duplication data before execution
     */
    private function validateDuplicationData()
    {
        // Re-validate basic selection
        $selectionValidation = $this->validateDuplicationSelection();
        if (!$selectionValidation['valid']) {
            return $selectionValidation;
        }

        // Validate editable preview data
        foreach ($this->selectedUesForDuplication as $ueId) {
            if (!isset($this->editablePreviewData[$ueId])) {
                return [
                    'valid' => false,
                    'message' => "Données manquantes pour l'UE ID {$ueId}. Veuillez revenir à l'étape de sélection."
                ];
            }

            $ueData = $this->editablePreviewData[$ueId]['ue'] ?? [];
            $ecData = $this->editablePreviewData[$ueId]['ecs'] ?? [];

            // Validate UE data
            $ueValidation = $this->validateUeData($ueData, $ueId);
            if (!$ueValidation['valid']) {
                return $ueValidation;
            }

            // Validate EC data
            $ecValidation = $this->validateEcData($ecData, $ueId);
            if (!$ecValidation['valid']) {
                return $ecValidation;
            }
        }

        return ['valid' => true, 'message' => ''];
    }

    /**
     * Validate UE data
     */
    private function validateUeData($ueData, $ueId)
    {
        // Check required fields
        if (empty($ueData['nom'])) {
            return [
                'valid' => false,
                'message' => "Le nom de l'UE (ID: {$ueId}) ne peut pas être vide."
            ];
        }

        if (empty($ueData['code'])) {
            return [
                'valid' => false,
                'message' => "Le code de l'UE (ID: {$ueId}) ne peut pas être vide."
            ];
        }

        if (!isset($ueData['credit']) || !is_numeric($ueData['credit']) || $ueData['credit'] < 0) {
            return [
                'valid' => false,
                'message' => "Le crédit de l'UE (ID: {$ueId}) doit être un nombre positif."
            ];
        }

        // Validate string lengths
        if (strlen($ueData['nom']) > 255) {
            return [
                'valid' => false,
                'message' => "Le nom de l'UE (ID: {$ueId}) ne peut pas dépasser 255 caractères."
            ];
        }

        if (strlen($ueData['code']) > 50) {
            return [
                'valid' => false,
                'message' => "Le code de l'UE (ID: {$ueId}) ne peut pas dépasser 50 caractères."
            ];
        }

        // Validate code format (alphanumeric and some special characters)
        if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $ueData['code'])) {
            return [
                'valid' => false,
                'message' => "Le code de l'UE (ID: {$ueId}) ne peut contenir que des lettres, chiffres, tirets, underscores et points."
            ];
        }

        // Check for duplicate codes within the same duplication batch
        $duplicateCodes = [];
        foreach ($this->editablePreviewData as $otherUeId => $otherData) {
            if ($otherUeId != $ueId && isset($otherData['ue']['code']) && $otherData['ue']['code'] === $ueData['code']) {
                $duplicateCodes[] = $otherUeId;
            }
        }

        if (!empty($duplicateCodes)) {
            return [
                'valid' => false,
                'message' => "Le code '{$ueData['code']}' est utilisé par plusieurs UE dans cette duplication. Chaque UE doit avoir un code unique."
            ];
        }

        return ['valid' => true, 'message' => ''];
    }

    /**
     * Validate EC data
     */
    private function validateEcData($ecData, $ueId)
    {
        $ecCodes = [];

        foreach ($ecData as $index => $ec) {
            // Check required fields
            if (empty($ec['nom'])) {
                return [
                    'valid' => false,
                    'message' => "Le nom de l'EC #{$index} de l'UE (ID: {$ueId}) ne peut pas être vide."
                ];
            }

            if (empty($ec['code'])) {
                return [
                    'valid' => false,
                    'message' => "Le code de l'EC #{$index} de l'UE (ID: {$ueId}) ne peut pas être vide."
                ];
            }

            // Validate string lengths
            if (strlen($ec['nom']) > 255) {
                return [
                    'valid' => false,
                    'message' => "Le nom de l'EC #{$index} de l'UE (ID: {$ueId}) ne peut pas dépasser 255 caractères."
                ];
            }

            if (strlen($ec['code']) > 50) {
                return [
                    'valid' => false,
                    'message' => "Le code de l'EC #{$index} de l'UE (ID: {$ueId}) ne peut pas dépasser 50 caractères."
                ];
            }

            // Validate code format (alphanumeric and some special characters)
            if (!preg_match('/^[a-zA-Z0-9_\-\.]+$/', $ec['code'])) {
                return [
                    'valid' => false,
                    'message' => "Le code de l'EC #{$index} de l'UE (ID: {$ueId}) ne peut contenir que des lettres, chiffres, tirets, underscores et points."
                ];
            }

            // Check for duplicate codes within the same UE
            if (in_array($ec['code'], $ecCodes)) {
                return [
                    'valid' => false,
                    'message' => "Le code '{$ec['code']}' est utilisé par plusieurs EC dans l'UE (ID: {$ueId}). Chaque EC doit avoir un code unique."
                ];
            }
            $ecCodes[] = $ec['code'];

            // Validate teacher assignment if provided
            if (!empty($ec['user_id'])) {
                if (!is_numeric($ec['user_id'])) {
                    return [
                        'valid' => false,
                        'message' => "L'enseignant assigné à l'EC #{$index} de l'UE (ID: {$ueId}) n'est pas valide."
                    ];
                }

                // Check if teacher exists and has the right role
                $teacher = User::whereHas('roles', fn($q) => $q->where('role_id', '=', 2))
                    ->where('id', $ec['user_id'])
                    ->first();

                if (!$teacher) {
                    return [
                        'valid' => false,
                        'message' => "L'enseignant assigné à l'EC #{$index} de l'UE (ID: {$ueId}) n'existe pas ou n'a pas le rôle d'enseignant."
                    ];
                }
            }
        }

        return ['valid' => true, 'message' => ''];
    }
}