<!-- UE/EC Duplication Modal -->
@if($showDuplicationModal)
<div class="modal fade show d-block" tabindex="-1" role="dialog" aria-hidden="true" wire:ignore.self>
    <div class="modal-dialog modal-xl modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title">
                    <i class="fa fa-copy me-2"></i>
                    Duplication d'UE et EC
                    @if($duplicationStep === 'selection')
                        - Sélection
                    @elseif($duplicationStep === 'preview')
                        - Aperçu et Modification
                    @elseif($duplicationStep === 'processing')
                        - Traitement en cours...
                    @endif
                </h5>
                <button type="button" class="btn-close btn-close-white" wire:click="closeDuplicationModal" aria-label="Close"></button>
            </div>

            <div class="modal-body">
                @if($duplicationStep === 'selection')
                    <!-- Step 1: Selection -->
                    <div class="row">
                        <!-- Left Column: UE Selection -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-list-check me-1"></i>
                                Sélectionner les UE à dupliquer
                            </h6>

                            <div class="alert alert-info">
                                <i class="fa fa-info-circle me-1"></i>
                                Sélectionnez une ou plusieurs UE à dupliquer. Les EC associés seront également dupliqués.
                            </div>

                            <!-- Filtres pour la sélection des UEs -->
                            <div class="card mb-3 duplication-filter-card">
                                <div class="card-header py-2">
                                    <h6 class="mb-0">
                                        <i class="fa fa-filter me-1"></i>
                                        Filtres de sélection
                                        <button class="btn btn-sm btn-outline-secondary float-end" wire:click="clearDuplicationFilters">
                                            <i class="fa fa-times me-1"></i>Effacer
                                        </button>
                                    </h6>
                                </div>
                                <div class="card-body py-2">
                                    <!-- Recherche -->
                                    <div class="mb-2">
                                        <input type="search" wire:model.debounce.300ms="duplicationQuery"
                                            class="form-control form-control-sm"
                                            placeholder="Rechercher par code, nom, parcours...">
                                    </div>

                                    <div class="row g-2">
                                        <!-- Filtre Année -->
                                        <div class="col-4">
                                            <select wire:model="duplicationFilterAnnee" class="form-select form-select-sm">
                                                <option value="">Toutes les années</option>
                                                @foreach($annees as $annee)
                                                    <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Filtre Parcours -->
                                        <div class="col-4">
                                            <select  id="duplication-parcours-select1" class="form-select form-select-sm" multiple>
                                                @foreach($parcours as $parcour)
                                                    <option value="{{ $parcour->id }}">{{ $parcour->sigle }}</option>
                                                @endforeach
                                            </select>
                                        </div>

                                        <!-- Filtre Semestre -->
                                        <div class="col-4">
                                            <select id="duplication-semestre-select" class="form-select form-select-sm" multiple>
                                                @foreach($semestres as $semestre)
                                                    <option value="{{ $semestre->id }}">{{ $semestre->nom }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Sélection tout/rien -->
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                        wire:model="selectAllUes"
                                        wire:click="toggleSelectAllUes"
                                        id="selectAllUes">
                                    <label class="form-check-label fw-bold" for="selectAllUes">
                                        Tout sélectionner/désélectionner
                                    </label>
                                </div>
                                @if(count($selectedUesForDuplication) > 0)
                                    <span class="badge bg-success">
                                        {{ count($selectedUesForDuplication) }} sélectionnée(s)
                                    </span>
                                @endif
                            </div>

                            <!-- Liste des UEs filtrées -->
                            <div class="duplication-ue-list p-3">
                                @php
                                    $filteredUes = $this->getFilteredUesForDuplication();
                                @endphp

                                @if($filteredUes->count() > 0)
                                    @foreach($filteredUes as $ue)
                                        <div class="form-check mb-2 duplication-ue-item p-2">
                                            <input class="form-check-input" type="checkbox"
                                                wire:click="toggleUeSelection({{ $ue->id }})"
                                                @if(in_array($ue->id, $selectedUesForDuplication)) checked @endif
                                                id="ue_{{ $ue->id }}">
                                            <label class="form-check-label w-100" for="ue_{{ $ue->id }}">
                                                <div class="d-flex justify-content-between align-items-start">
                                                    <div>
                                                        <strong>{{ $ue->code }}</strong> - {{ $ue->nom }}
                                                        <div class="small text-muted">
                                                            {{ $ue->parcours->sigle }} {{ $ue->niveau->nom }} - {{ $ue->semestre->nom }}
                                                            <span class="badge bg-secondary ms-1">{{ $ue->matiere->count() }} EC(s)</span>
                                                            <span class="badge bg-info ms-1">{{ $ue->annee->nom }}</span>
                                                        </div>
                                                    </div>
                                                    <span class="badge bg-primary">{{ $ue->credit }} crédits</span>
                                                </div>
                                            </label>
                                        </div>
                                    @endforeach
                                @else
                                    <div class="text-center text-muted py-3">
                                        <i class="fa fa-search fa-2x mb-2"></i>
                                        <p>Aucune UE trouvée avec les critères actuels</p>
                                    </div>
                                @endif
                            </div>

                            @if(count($selectedUesForDuplication) > 0)
                                <div class="duplication-stats mt-3">
                                    <i class="fa fa-check me-1"></i>
                                    <strong>{{ count($selectedUesForDuplication) }} UE(s) sélectionnée(s)</strong> pour la duplication
                                </div>
                            @endif
                        </div>

                        <!-- Right Column: Target Selection -->
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">
                                <i class="fa fa-target me-1"></i>
                                Destination de la duplication
                            </h6>

                            <!-- Academic Year Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">Année universitaire de destination <span class="text-danger">*</span></label>
                                <select class="form-select" wire:model="targetAnneeId">
                                    <option value="">Sélectionner une année...</option>
                                    @foreach($annees as $annee)
                                        <option value="{{ $annee->id }}">{{ $annee->nom }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Parcours Multi-Selection -->
                            <div class="mb-4">
                                <label class="form-label fw-semibold">Parcours de destination <span class="text-danger">*</span></label>
                                <select class="form-select" id="duplication-parcours-select" multiple wire:ignore>
                                    @foreach($parcours as $parcour)
                                        <option value="{{ $parcour->id }}">{{ $parcour->sigle }} - {{ $parcour->nom }}</option>
                                    @endforeach
                                </select>
                                <div class="form-text">
                                    Utilisez Ctrl+Clic pour sélectionner plusieurs parcours
                                </div>
                            </div>

                            @if(count($targetParcours) > 0)
                                <div class="alert alert-success">
                                    <i class="fa fa-check me-1"></i>
                                    {{ count($targetParcours) }} parcours sélectionné(s)
                                </div>
                            @endif

                            <!-- Summary -->
                            @if(count($selectedUesForDuplication) > 0 && count($targetParcours) > 0)
                                <div class="alert alert-primary">
                                    <h6 class="mb-2">Résumé de la duplication :</h6>
                                    <ul class="mb-0">
                                        <li>{{ count($selectedUesForDuplication) }} UE(s) à dupliquer</li>
                                        <li>{{ count($targetParcours) }} parcours de destination</li>
                                        <li><strong>{{ count($selectedUesForDuplication) * count($targetParcours) }}</strong> UE(s) seront créées</li>
                                    </ul>
                                </div>
                            @endif
                        </div>
                    </div>

                @elseif($duplicationStep === 'preview')
                    <!-- Step 2: Preview and Edit -->
                    <div class="alert alert-warning">
                        <i class="fa fa-exclamation-triangle me-1"></i>
                        <strong>Aperçu avant duplication</strong> - Vous pouvez modifier les données avant de confirmer la duplication.
                    </div>

                    <div class="accordion" id="previewAccordion">
                        @foreach($duplicationPreview as $ueId => $preview)
                            <div class="accordion-item">
                                <h2 class="accordion-header" id="heading{{ $ueId }}">
                                    <button class="accordion-button" type="button" data-bs-toggle="collapse" 
                                        data-bs-target="#collapse{{ $ueId }}" aria-expanded="true" aria-controls="collapse{{ $ueId }}">
                                        <strong>{{ $preview['ue']['code'] }} - {{ $preview['ue']['nom'] }}</strong>
                                        <span class="badge bg-primary ms-2">{{ count($preview['ecs']) }} EC(s)</span>
                                        @if(count($preview['conflicts']) > 0)
                                            <span class="badge bg-warning ms-1">{{ count($preview['conflicts']) }} conflit(s)</span>
                                        @endif
                                    </button>
                                </h2>
                                <div id="collapse{{ $ueId }}" class="accordion-collapse collapse show" 
                                    aria-labelledby="heading{{ $ueId }}" data-bs-parent="#previewAccordion">
                                    <div class="accordion-body">
                                        <!-- UE Data -->
                                        <div class="row mb-4">
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Données UE</h6>
                                                <div class="mb-2">
                                                    <label class="form-label">Code</label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.{{ $ueId }}.ue.code">
                                                </div>
                                                <div class="mb-2">
                                                    <label class="form-label">Nom</label>
                                                    <input type="text" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.{{ $ueId }}.ue.nom">
                                                </div>
                                                <div class="mb-2">
                                                    <label class="form-label">Crédit</label>
                                                    <input type="number" class="form-control form-control-sm" 
                                                        wire:model="editablePreviewData.{{ $ueId }}.ue.credit">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <h6 class="fw-bold">Parcours de destination</h6>
                                                <ul class="list-group list-group-flush">
                                                    @foreach($preview['target_parcours'] as $parcour)
                                                        <li class="list-group-item px-0 py-1">
                                                            <i class="fa fa-arrow-right me-1 text-primary"></i>
                                                            {{ $parcour['sigle'] }} - {{ $parcour['nom'] }}
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            </div>
                                        </div>

                                        <!-- Conflicts Warning -->
                                        @if(count($preview['conflicts']) > 0)
                                            <div class="alert alert-warning">
                                                <h6 class="fw-bold">Conflits détectés :</h6>
                                                @foreach($preview['conflicts'] as $conflict)
                                                    <div>
                                                        <i class="fa fa-exclamation-triangle me-1"></i>
                                                        {{ $conflict['message'] }} ({{ $conflict['parcours_nom'] }})
                                                    </div>
                                                @endforeach
                                            </div>
                                        @endif

                                        <!-- EC Data -->
                                        @if(count($preview['ecs']) > 0)
                                            <h6 class="fw-bold mb-3">Éléments Constitutifs (EC)</h6>
                                            <div class="table-responsive">
                                                <table class="table table-sm table-bordered">
                                                    <thead>
                                                        <tr>
                                                            <th>Code</th>
                                                            <th>Nom</th>
                                                            <th>Enseignant</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        @foreach($preview['ecs'] as $index => $ec)
                                                            <tr>
                                                                <td>
                                                                    <input type="text" class="form-control form-control-sm" 
                                                                        wire:model="editablePreviewData.{{ $ueId }}.ecs.{{ $index }}.code">
                                                                </td>
                                                                <td>
                                                                    <input type="text" class="form-control form-control-sm" 
                                                                        wire:model="editablePreviewData.{{ $ueId }}.ecs.{{ $index }}.nom">
                                                                </td>
                                                                <td>
                                                                    <select class="form-select form-select-sm" 
                                                                        wire:model="editablePreviewData.{{ $ueId }}.ecs.{{ $index }}.user_id">
                                                                        <option value="">-- Non assigné --</option>
                                                                        @foreach($enseignants as $enseignant)
                                                                            <option value="{{ $enseignant->id }}">
                                                                                {{ $enseignant->nom }} {{ $enseignant->prenom }}
                                                                            </option>
                                                                        @endforeach
                                                                    </select>
                                                                </td>
                                                            </tr>
                                                        @endforeach
                                                    </tbody>
                                                </table>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                @elseif($duplicationStep === 'processing')
                    <!-- Step 3: Processing -->
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary mb-3" role="status">
                            <span class="visually-hidden">Traitement en cours...</span>
                        </div>
                        <h5>Duplication en cours...</h5>
                        <p class="text-muted">Veuillez patienter pendant la création des UE et EC.</p>
                    </div>
                @endif
            </div>

            <div class="modal-footer bg-light border-top d-flex justify-content-between align-items-center">
                <!-- Navigation Steps Indicator -->
                <div class="d-flex align-items-center">
                    <div class="step-indicator d-flex align-items-center">
                        <div class="step-item @if($duplicationStep === 'selection') active @elseif(in_array($duplicationStep, ['preview', 'processing'])) completed @endif">
                            <span class="step-number">1</span>
                            <span class="step-label d-none d-md-inline ms-2">Sélection</span>
                        </div>
                        <div class="step-divider mx-2">
                            <i class="fa fa-chevron-right text-muted"></i>
                        </div>
                        <div class="step-item @if($duplicationStep === 'preview') active @elseif($duplicationStep === 'processing') completed @endif">
                            <span class="step-number">2</span>
                            <span class="step-label d-none d-md-inline ms-2">Aperçu</span>
                        </div>
                        <div class="step-divider mx-2">
                            <i class="fa fa-chevron-right text-muted"></i>
                        </div>
                        <div class="step-item @if($duplicationStep === 'processing') active @endif">
                            <span class="step-number">3</span>
                            <span class="step-label d-none d-md-inline ms-2">Traitement</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="d-flex gap-2">
                    @if($duplicationStep === 'selection')
                        <button type="button" class="btn btn-outline-secondary" wire:click="closeDuplicationModal">
                            <i class="fa fa-times me-1"></i>Annuler
                        </button>
                        <button type="button" class="btn btn-primary" wire:click="proceedToPreview"
                            @if(count($selectedUesForDuplication) === 0 || count($targetParcours) === 0 || empty($targetAnneeId)) disabled @endif>
                            <i class="fa fa-arrow-right me-1"></i>Continuer vers l'aperçu
                        </button>
                    @elseif($duplicationStep === 'preview')
                        <button type="button" class="btn btn-outline-secondary" wire:click="backToSelection">
                            <i class="fa fa-arrow-left me-1"></i>Retour à la sélection
                        </button>
                        <button type="button" class="btn btn-success" wire:click="executeDuplication">
                            <i class="fa fa-copy me-1"></i>Confirmer la duplication
                        </button>
                    @elseif($duplicationStep === 'processing')
                        <button type="button" class="btn btn-outline-secondary" disabled>
                            <i class="fa fa-spinner fa-spin me-1"></i>Traitement en cours...
                        </button>
                        <button type="button" class="btn btn-secondary" wire:click="closeDuplicationModal">
                            <i class="fa fa-times me-1"></i>Fermer
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade show"></div>
@endif
